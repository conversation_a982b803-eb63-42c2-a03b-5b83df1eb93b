"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/layout/SidebarContent.tsx":
/*!**************************************************!*\
  !*** ./src/components/layout/SidebarContent.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* harmony import */ var _quiz_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quiz-card */ \"(app-pages-browser)/./src/components/layout/quiz-card.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SidebarContent(param) {\n    let { onNewSession, chatHistory = [], onSubjectSelect, currentSubject, isLoading, currentTopic, subjectName, topicName } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userStreak, setUserStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isStreakLoading, setIsStreakLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [subjectMap, setSubjectMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [subjectsLoading, setSubjectsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // Fetch subjects for mapping IDs to names\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SidebarContent.useEffect\": ()=>{\n            const fetchSubjects = {\n                \"SidebarContent.useEffect.fetchSubjects\": async ()=>{\n                    try {\n                        setSubjectsLoading(true);\n                        const allSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n                        const mapping = {};\n                        allSubjects.forEach({\n                            \"SidebarContent.useEffect.fetchSubjects\": (subject)=>{\n                                mapping[subject._id] = subject;\n                            }\n                        }[\"SidebarContent.useEffect.fetchSubjects\"]);\n                        setSubjectMap(mapping);\n                    } catch (error) {\n                        console.error('Failed to fetch subjects:', error);\n                    } finally{\n                        setSubjectsLoading(false);\n                    }\n                }\n            }[\"SidebarContent.useEffect.fetchSubjects\"];\n            fetchSubjects();\n        }\n    }[\"SidebarContent.useEffect\"], []);\n    const subjects = isLoading || !Array.isArray(chatHistory) ? [] : [\n        ...new Set(chatHistory.reduce((acc, item)=>{\n            const itemSubjects = (item === null || item === void 0 ? void 0 : item.subjects) || [];\n            return acc.concat(itemSubjects);\n        }, []))\n    ];\n    // Function to get display name for subject\n    const getSubjectDisplayName = (subjectId)=>{\n        const subject = subjectMap[subjectId];\n        return subject ? subject.name : subjectId; // Fallback to ID if not found\n    };\n    const handleSubjectClick = (subject)=>{\n        onSubjectSelect(subject);\n        setIsOpen(false);\n    };\n    const handleNewSession = ()=>{\n        onNewSession();\n        setIsOpen(false);\n    };\n    const fetchStreakData = async ()=>{\n        setIsStreakLoading(true);\n        try {\n            const token = localStorage.getItem('accessToken');\n            if (!token) {\n                setUserStreak({\n                    streak: 0\n                });\n                setIsStreakLoading(false);\n                return;\n            }\n            const response = await fetch(\"\".concat(\"http://localhost:3000\" || 0, \"/chat/chat-streak\"), {\n                method: 'GET',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) {\n                // For any error, just set streak to 0 instead of throwing\n                console.warn(\"Chat streak API returned \".concat(response.status, \", defaulting to 0\"));\n                setUserStreak({\n                    streak: 0\n                });\n            } else {\n                const data = await response.json();\n                setUserStreak(data);\n            }\n        } catch (error) {\n            console.error('Error fetching user streak data:', error);\n            setUserStreak({\n                streak: 0\n            });\n        } finally{\n            setIsStreakLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SidebarContent.useEffect\": ()=>{\n            fetchStreakData();\n        }\n    }[\"SidebarContent.useEffect\"], []);\n    const SidebarItems = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" py-4 bg-gray-50 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            src: \"/assets/logo/studubuddy-logo-new.png\",\n                            alt: \"StudyBuddy Logo\",\n                            width: 160,\n                            height: 40,\n                            className: \"h-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 19\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: handleNewSession,\n                    className: \"w-full bg-[#309CEC] text-white text-[18px] font-bold py-2 rounded-[76px] hover:bg-[#309CEC]/80 transition-colors\",\n                    children: \"+ Start a New Session\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-sm text-gray-400\",\n                            children: \"Recent Subjects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                            className: \"h-[250px] w-full pr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: isLoading || subjectsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-center py-4\",\n                                    children: \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this) : subjects.length > 0 ? subjects.map((subject, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full text-[16px] py-2 rounded-[76px] transition-colors \".concat(currentSubject === subject ? 'bg-[#309CEC] text-[#F9F5FF]' : 'text-[#858585] bg-[#F9F5FF] hover:bg-[#F9F5FF]/70'),\n                                        onClick: ()=>handleSubjectClick(subject),\n                                        children: getSubjectDisplayName(subject)\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-center py-4\",\n                                    children: \"No subjects yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_quiz_card__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        currentSubject: currentSubject,\n                        currentTopic: currentTopic,\n                        subjectName: subjectName,\n                        topicName: topicName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n            lineNumber: 147,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.Sheet, {\n                    open: isOpen,\n                    onOpenChange: setIsOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                className: \"fixed top-4 left-4 z-50 w-16 h-16 p-0 hover:bg-[#4024B9]/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-8 w-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetContent, {\n                            side: \"left\",\n                            className: \"w-80 bg-white border-r border-[#309CEC] p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetHeader, {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_4__.SheetTitle, {\n                                        className: \"text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItems, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:block w-80 bg-white rounded-[12px] border border-[#309CEC] p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItems, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\SidebarContent.tsx\",\n                lineNumber: 242,\n                columnNumber: 1\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SidebarContent, \"YzLxkVu4wS7T4ygT1f4hVTueHKM=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = SidebarContent;\nvar _c;\n$RefreshReg$(_c, \"SidebarContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/SidebarContent.tsx\n"));

/***/ })

});