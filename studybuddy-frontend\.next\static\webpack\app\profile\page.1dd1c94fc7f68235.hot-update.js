"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/components/layout/recommendations.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/recommendations.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Recommendations: () => (/* binding */ Recommendations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\n\n\n\nfunction Recommendations(param) {\n    let { userData } = param;\n    const recommendedSubjects = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getNonRepeatingValues)(userData.subjects, _lib_utils__WEBPACK_IMPORTED_MODULE_2__.subjectOptions);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 rounded-[22.5px] border-2 border-blue-200 bg-white h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 rounded-[50%] overflow-hidden bg-blue-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: \"/assets/buddy/Joy-profile-icon.svg\",\n                            alt: \"Recommendation mascot\",\n                            width: 48,\n                            height: 48,\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-800\",\n                                children: [\n                                    userData.name,\n                                    \", This is what you should\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"focus on in my opinion...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-sm text-gray-600\",\n                children: \"Recommended Subjects\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_1__.ScrollArea, {\n                className: \"h-[300px] pr-4 mt-4\",\n                style: {\n                    '--scrollbar-size': '4px',\n                    '--scrollbar-thumb-color': 'rgba(255, 255, 255, 0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: recommendedSubjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            value: subject,\n                            className: \"w-full bg-[#3B3B3B] text-white placeholder:text-white/50 py-4 px-4 rounded-[10.79px]\",\n                            readOnly: true\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 19\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\recommendations.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = Recommendations;\nvar _c;\n$RefreshReg$(_c, \"Recommendations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/recommendations.tsx\n"));

/***/ })

});