"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/subject-card */ \"(app-pages-browser)/./src/components/dashboard/subject-card.tsx\");\n/* harmony import */ var _components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/recommendation-card */ \"(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(app-pages-browser)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst subjectColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-red-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-yellow-500\"\n];\n// Helper function to analyze user's learning patterns from chat history\nconst analyzeUserLearningPatterns = (chatHistory, subjects)=>{\n    const subjectMap = new Map();\n    subjects.forEach((subject)=>{\n        subjectMap.set(subject._id, subject);\n    });\n    const analysis = new Map();\n    chatHistory.forEach((historyItem)=>{\n        // Check if this is recent activity (within last 7 days)\n        const isRecent = new Date(historyItem.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);\n        historyItem.subjectWise.forEach((subjectData)=>{\n            const subject = subjectMap.get(subjectData.subject);\n            if (subject) {\n                const existing = analysis.get(subject._id) || {\n                    subject,\n                    queryCount: 0,\n                    topics: new Set(),\n                    recentActivity: false\n                };\n                existing.queryCount += subjectData.queries.length;\n                existing.recentActivity = existing.recentActivity || isRecent;\n                // Extract topics from queries (simplified - could be enhanced with NLP)\n                subjectData.queries.forEach((query)=>{\n                    subject.topics.forEach((topic)=>{\n                        if (query.query.toLowerCase().includes(topic.name.toLowerCase()) || query.response.toLowerCase().includes(topic.name.toLowerCase())) {\n                            existing.topics.add(topic.name);\n                        }\n                    });\n                });\n                analysis.set(subject._id, existing);\n            }\n        });\n    });\n    return analysis;\n};\n// Helper function to generate quiz recommendations based on analysis\nconst generateQuizRecommendations = (analysis)=>{\n    const recommendations = [];\n    const colors = [\n        {\n            bg: \"bg-blue-50\",\n            button: \"bg-blue-500\"\n        },\n        {\n            bg: \"bg-green-50\",\n            button: \"bg-green-500\"\n        },\n        {\n            bg: \"bg-orange-50\",\n            button: \"bg-orange-500\"\n        },\n        {\n            bg: \"bg-purple-50\",\n            button: \"bg-purple-500\"\n        },\n        {\n            bg: \"bg-red-50\",\n            button: \"bg-red-500\"\n        },\n        {\n            bg: \"bg-indigo-50\",\n            button: \"bg-indigo-500\"\n        }\n    ];\n    // Sort subjects by query count (most asked questions first)\n    const sortedSubjects = Array.from(analysis.entries()).sort((param, param1)=>{\n        let [, a] = param, [, b] = param1;\n        return b.queryCount - a.queryCount;\n    }).slice(0, 6) // Limit to top 6 subjects\n    ;\n    sortedSubjects.forEach((param, index)=>{\n        let [subjectId, data] = param;\n        const colorIndex = index % colors.length;\n        const { subject, queryCount, topics, recentActivity } = data;\n        // Generate recommendation score based on activity\n        let score = \"Quick Review\";\n        if (queryCount > 10) {\n            score = \"Practice Needed\";\n        } else if (queryCount > 5) {\n            score = \"Review Required\";\n        } else if (recentActivity) {\n            score = \"Fresh Topic\";\n        }\n        // If we have specific topics, recommend quiz for the most discussed topic\n        const topicArray = Array.from(topics);\n        const recommendedTopic = topicArray.length > 0 ? topicArray[0] : undefined;\n        const topicObj = recommendedTopic ? subject.topics.find((t)=>t.name === recommendedTopic) : undefined;\n        recommendations.push({\n            subject: subject.name,\n            score,\n            color: colors[colorIndex].bg,\n            buttonColor: colors[colorIndex].button,\n            subjectId: subject._id,\n            topicId: topicObj === null || topicObj === void 0 ? void 0 : topicObj._id,\n            topicName: topicObj === null || topicObj === void 0 ? void 0 : topicObj.name\n        });\n    });\n    // If no chat history, provide some default recommendations\n    if (recommendations.length === 0) {\n        return [\n            {\n                subject: \"Mathematics\",\n                score: \"Get Started\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Physics\",\n                score: \"Explore\",\n                color: \"bg-green-50\",\n                buttonColor: \"bg-green-500\"\n            },\n            {\n                subject: \"Chemistry\",\n                score: \"Try Now\",\n                color: \"bg-orange-50\",\n                buttonColor: \"bg-orange-500\"\n            }\n        ];\n    }\n    return recommendations;\n};\nfunction Dashboard() {\n    _s();\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            loadSubjects();\n            loadRecommendations();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const loadSubjects = async ()=>{\n        try {\n            setLoading(true);\n            const apiSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            const localSubjects = apiSubjects.map((subject, index)=>({\n                    id: subject._id,\n                    subject: subject.name,\n                    color: subjectColors[index % subjectColors.length],\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }));\n            setSubjects(localSubjects);\n        } catch (error) {\n            console.error('Failed to load subjects:', error);\n            // Fallback to default subjects\n            setSubjects([\n                {\n                    id: \"1\",\n                    subject: \"Maths\",\n                    color: \"bg-blue-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"2\",\n                    subject: \"Physics\",\n                    color: \"bg-orange-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"3\",\n                    subject: \"Biology\",\n                    color: \"bg-red-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"4\",\n                    subject: \"Chemistry\",\n                    color: \"bg-green-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRecommendations = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch chat history to analyze user's learning patterns\n            const chatResponse = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat/chat-history\"), {\n                headers: getAuthHeaders()\n            });\n            if (!chatResponse.ok) {\n                throw new Error('Failed to fetch chat history');\n            }\n            const chatData = await chatResponse.json();\n            const chatHistory = Array.isArray(chatData.data) ? chatData.data : [\n                chatData.data\n            ];\n            // Fetch all subjects to get subject and topic details\n            const allSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            // Analyze chat history to generate intelligent recommendations\n            const subjectAnalysis = analyzeUserLearningPatterns(chatHistory, allSubjects);\n            // Generate recommendations based on analysis\n            const intelligentRecommendations = generateQuizRecommendations(subjectAnalysis);\n            setRecommendations(intelligentRecommendations);\n        } catch (error) {\n            console.error('Error loading recommendations:', error);\n            // Fallback to some default recommendations\n            setRecommendations([\n                {\n                    subject: \"Mathematics\",\n                    score: \"Practice Needed\",\n                    color: \"bg-blue-50\",\n                    buttonColor: \"bg-blue-500\"\n                },\n                {\n                    subject: \"Physics\",\n                    score: \"Review Required\",\n                    color: \"bg-orange-50\",\n                    buttonColor: \"bg-orange-500\"\n                },\n                {\n                    subject: \"Chemistry\",\n                    score: \"Quick Quiz\",\n                    color: \"bg-green-50\",\n                    buttonColor: \"bg-green-500\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__.AppSidebar, {\n                currentPage: \"dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-medium text-gray-800 mb-4\",\n                                    children: \"Welcome back, Student \\uD83D\\uDC4B\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"Continue Learning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: subjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__.SubjectCard, {\n                                                subject: subject.subject,\n                                                color: subject.color,\n                                                image: subject.image,\n                                                subjectId: subject.id\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Smart Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__.RecommendationCard, {\n                                                subject: rec.subject,\n                                                score: rec.score,\n                                                color: rec.color,\n                                                buttonColor: rec.buttonColor,\n                                                subjectId: rec.subjectId,\n                                                topicId: rec.topicId,\n                                                topicName: rec.topicName\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"OJt/C7Hd+E1pZMf9rSalgfCehPo=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});