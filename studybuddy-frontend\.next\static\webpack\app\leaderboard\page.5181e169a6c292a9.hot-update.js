"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leaderboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/profile-card.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/profile-card.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileCard: () => (/* binding */ ProfileCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Flame!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _lib_api_leaderboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/leaderboard */ \"(app-pages-browser)/./src/lib/api/leaderboard.ts\");\n\n\n\nfunction ProfileCard(param) {\n    let { user } = param;\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl border border-blue-200 p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-20 h-20 rounded-full mx-auto mb-4 border-4 border-blue-100 bg-gray-100 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 text-sm\",\n                        children: \"No Data\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"font-semibold text-lg text-gray-900 mb-2\",\n                    children: \"Not Ranked\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"Start chatting to get ranked!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl border border-blue-200 p-6 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: user.profileImage || \"/assets/buddy/default_profile_pic.png\",\n                alt: user.name,\n                className: \"w-20 h-20 rounded-full mx-auto mb-4 border-4 border-blue-100\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"font-semibold text-lg text-gray-900 mb-2\",\n                children: user.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium inline-flex items-center gap-1 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    user.sparkPoints\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium\",\n                children: [\n                    \"#\",\n                    user.rank,\n                    (0,_lib_api_leaderboard__WEBPACK_IMPORTED_MODULE_1__.getPositionSuffix)(user.rank),\n                    \" Position\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-500 text-sm mt-4\",\n                children: [\n                    \"Congrats \",\n                    user.name.split(' ')[0],\n                    \",\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    \"Keep Crushing!\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\profile-card.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_c = ProfileCard;\nvar _c;\n$RefreshReg$(_c, \"ProfileCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/profile-card.tsx\n"));

/***/ })

});