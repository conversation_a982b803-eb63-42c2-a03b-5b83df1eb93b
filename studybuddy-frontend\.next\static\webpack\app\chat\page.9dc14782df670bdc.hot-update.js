"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_ChatHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/ChatHeader */ \"(app-pages-browser)/./src/components/layout/ChatHeader.tsx\");\n/* harmony import */ var _components_layout_ChatInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/ChatInput */ \"(app-pages-browser)/./src/components/layout/ChatInput.tsx\");\n/* harmony import */ var _components_layout_SidebarContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/SidebarContent */ \"(app-pages-browser)/./src/components/layout/SidebarContent.tsx\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* harmony import */ var _components_ui_autoScrollChatArea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/autoScrollChatArea */ \"(app-pages-browser)/./src/components/ui/autoScrollChatArea.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/raise-issue-modal */ \"(app-pages-browser)/./src/components/ui/raise-issue-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// import {subjectOptions} from '@/lib/utils'\n// import SubjectDialog from \"@/components/ui/subjectSectionDialog\";\n\n\n\n\nfunction ChatInterface() {\n    _s();\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subjectName, setSubjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [topicName, setTopicName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showDashboard, setShowDashboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRaiseIssueModal, setShowRaiseIssueModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Function to replace subject IDs with subject names in responses\n    const replaceSubjectIdsInResponse = (response)=>{\n        if (!subjectName || !selectedSubject) return response;\n        // Replace subject ID with subject name in the response\n        const subjectIdPattern = new RegExp(selectedSubject, 'g');\n        return response.replace(subjectIdPattern, subjectName);\n    };\n    const fetchChatHistory = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat/chat-history\"), {\n                headers: getAuthHeaders()\n            });\n            const responseData = await response.json();\n            const historyData = Array.isArray(responseData.data) ? responseData.data : responseData.data ? [\n                responseData.data\n            ] : [];\n            setChatHistory(historyData);\n            // Process the history data for the current session\n            const sessionData = {};\n            historyData.forEach((item)=>{\n                var _item_subjectWise;\n                (_item_subjectWise = item.subjectWise) === null || _item_subjectWise === void 0 ? void 0 : _item_subjectWise.forEach((subjectData)=>{\n                    if (!sessionData[subjectData.subject]) {\n                        sessionData[subjectData.subject] = [];\n                    }\n                    sessionData[subjectData.subject] = [\n                        ...sessionData[subjectData.subject],\n                        ...subjectData.queries\n                    ];\n                });\n            });\n            setCurrentSession(sessionData);\n        } catch (error) {\n            console.error(\"Error fetching chat history:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            fetchChatHistory();\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Handle subject and topic parameters from URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const subjectFromUrl = searchParams.get('subject');\n            const topicFromUrl = searchParams.get('topic');\n            const subjectNameFromUrl = searchParams.get('subjectName');\n            const topicNameFromUrl = searchParams.get('topicName');\n            if (subjectFromUrl) {\n                setSelectedSubject(subjectFromUrl);\n                setShowDashboard(false);\n            } else {\n                // Only show dashboard if no subject parameter is present\n                setShowDashboard(true);\n            }\n            if (topicFromUrl) {\n                setSelectedTopic(topicFromUrl);\n            }\n            if (subjectNameFromUrl) {\n                setSubjectName(decodeURIComponent(subjectNameFromUrl));\n            }\n            if (topicNameFromUrl) {\n                setTopicName(decodeURIComponent(topicNameFromUrl));\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        searchParams\n    ]);\n    // Redirect to dashboard if no subject is selected and we should show dashboard\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Add a small delay to ensure URL parameters are processed first\n            const timer = setTimeout({\n                \"ChatInterface.useEffect.timer\": ()=>{\n                    if (showDashboard && !selectedSubject && !searchParams.get('subject')) {\n                        router.push('/dashboard');\n                    }\n                }\n            }[\"ChatInterface.useEffect.timer\"], 100);\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        showDashboard,\n        selectedSubject,\n        router,\n        searchParams\n    ]);\n    // Rest of the component remains the same...\n    const handleSubjectSelect = (subject)=>{\n        setSelectedSubject(subject);\n        setShowDashboard(false);\n        if (currentSession[subject]) {\n            const subjectMessages = currentSession[subject].flatMap((query)=>[\n                    {\n                        content: query.query,\n                        isUser: true,\n                        lastMessage: false\n                    },\n                    {\n                        content: query.response,\n                        isUser: false,\n                        lastMessage: false\n                    }\n                ]);\n            setMessages(subjectMessages);\n        } else {\n            setMessages([]);\n        }\n    };\n    const handleNewSession = ()=>{\n        // Navigate to dashboard instead of showing dialog\n        router.push('/dashboard');\n    };\n    const handleSendMessage = async (message)=>{\n        if (!selectedSubject || !message.trim()) return;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    content: message,\n                    isUser: true,\n                    lastMessage: false\n                }\n            ]);\n        setIsTyping(true);\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat?subject=\").concat(selectedSubject, \"&query=\").concat(encodeURIComponent(message)), {\n                headers: getAuthHeaders()\n            });\n            const data = await response.json();\n            const newQuery = {\n                query: message,\n                response: data.response,\n                tokensUsed: data.tokensUsed || 0,\n                // lastMessage: true,\n                _id: Date.now().toString(),\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        content: data.response,\n                        isUser: false,\n                        lastMessage: true\n                    }\n                ]);\n            setIsTyping(false);\n            // Ensure lastMessage is updated only for the latest query\n            setCurrentSession((prev)=>{\n                const updatedQueries = [\n                    ...prev[selectedSubject] || [],\n                    newQuery\n                ];\n                return {\n                    ...prev,\n                    [selectedSubject]: updatedQueries\n                };\n            });\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        content: \"Sorry, there was an error processing your request.\",\n                        isUser: false,\n                        lastMessage: false\n                    }\n                ]);\n        }\n    };\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function to scroll to bottom\n    const scrollToBottom = ()=>{\n        if (scrollAreaRef.current) {\n            const scrollContainer = scrollAreaRef.current;\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    console.log(messages, 'messages-chat');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen p-2 text-black bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SidebarContent__WEBPACK_IMPORTED_MODULE_4__.SidebarContent, {\n                        onNewSession: handleNewSession,\n                        chatHistory: chatHistory,\n                        onSubjectSelect: handleSubjectSelect,\n                        currentSubject: selectedSubject,\n                        isLoading: isLoading,\n                        currentTopic: selectedTopic,\n                        subjectName: subjectName,\n                        topicName: topicName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col bg-white rounded-[12px] border border-[#309CEC]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_ChatHeader__WEBPACK_IMPORTED_MODULE_2__.ChatHeader, {\n                                subjectName: subjectName,\n                                topicName: topicName,\n                                onRaiseIssue: ()=>setShowRaiseIssueModal(true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_autoScrollChatArea__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                messages: messages,\n                                isTyping: isTyping\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_ChatInput__WEBPACK_IMPORTED_MODULE_3__.ChatInput, {\n                                onSendMessage: handleSendMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showRaiseIssueModal,\n                onClose: ()=>setShowRaiseIssueModal(false),\n                currentSubject: subjectName,\n                currentTopic: topicName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"czaoKFik17eZVNOHKD58aNkDrtw=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});