"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leaderboard/page",{

/***/ "(app-pages-browser)/./src/app/leaderboard/page.tsx":
/*!**************************************!*\
  !*** ./src/app/leaderboard/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LeaderboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_dashboard_podium__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/podium */ \"(app-pages-browser)/./src/components/dashboard/podium.tsx\");\n/* harmony import */ var _components_dashboard_leaderboard_list__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/leaderboard-list */ \"(app-pages-browser)/./src/components/dashboard/leaderboard-list.tsx\");\n/* harmony import */ var _components_dashboard_profile_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/profile-card */ \"(app-pages-browser)/./src/components/dashboard/profile-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(app-pages-browser)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _lib_api_leaderboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leaderboard */ \"(app-pages-browser)/./src/lib/api/leaderboard.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction LeaderboardPage() {\n    _s();\n    const [leaderboardUsers, setLeaderboardUsers] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)([]);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null);\n    const [topPerformers, setTopPerformers] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"\");\n    // Filter states\n    const [currentPeriod, setCurrentPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)('all');\n    const [currentSubject, setCurrentSubject] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"\");\n    const [currentClass, setCurrentClass] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(\"\");\n    // Fetch leaderboard data\n    const fetchLeaderboard = async ()=>{\n        try {\n            setIsLoading(true);\n            const filters = {\n                period: currentPeriod,\n                subject: currentSubject || undefined,\n                class: currentClass || undefined,\n                limit: 50\n            };\n            const [leaderboardData, topPerformersData] = await Promise.all([\n                _lib_api_leaderboard__WEBPACK_IMPORTED_MODULE_8__.LeaderboardAPI.getLeaderboard(filters),\n                _lib_api_leaderboard__WEBPACK_IMPORTED_MODULE_8__.LeaderboardAPI.getTopPerformers(filters)\n            ]);\n            setLeaderboardUsers(leaderboardData.users);\n            setCurrentUser(leaderboardData.currentUser || null);\n            setTopPerformers(topPerformersData.topThree);\n        } catch (error) {\n            console.error('Failed to fetch leaderboard:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Search users\n    const handleSearch = async (query)=>{\n        if (!query.trim()) {\n            fetchLeaderboard();\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const filters = {\n                period: currentPeriod,\n                subject: currentSubject || undefined,\n                class: currentClass || undefined\n            };\n            const searchData = await _lib_api_leaderboard__WEBPACK_IMPORTED_MODULE_8__.LeaderboardAPI.searchUsers(query, filters);\n            setLeaderboardUsers(searchData.users);\n        } catch (error) {\n            console.error('Failed to search users:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Filter handlers\n    const handlePeriodChange = (period)=>{\n        setCurrentPeriod(period);\n    };\n    const handleSubjectChange = (subject)=>{\n        setCurrentSubject(subject);\n    };\n    const handleClassChange = (classFilter)=>{\n        setCurrentClass(classFilter);\n    };\n    // Fetch data on component mount and filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)({\n        \"LeaderboardPage.useEffect\": ()=>{\n            fetchLeaderboard();\n        }\n    }[\"LeaderboardPage.useEffect\"], [\n        currentPeriod,\n        currentSubject,\n        currentClass\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__.AppSidebar, {\n                currentPage: \"dashboard/leaderboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Leaderboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 border border-orange-200 rounded-lg p-4 flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Earn sparks (\\uD83D\\uDD25)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" and climb the leaderboards by showing up every day, staying consistent, and getting your answers right.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_podium__WEBPACK_IMPORTED_MODULE_1__.Podium, {\n                                users: leaderboardUsers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_leaderboard_list__WEBPACK_IMPORTED_MODULE_2__.LeaderboardList, {\n                                            users: leaderboardUsers\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_profile_card__WEBPACK_IMPORTED_MODULE_3__.ProfileCard, {\n                                            user: currentUser\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_profile_card__WEBPACK_IMPORTED_MODULE_3__.ProfileCard, {\n                                    user: currentUser\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaderboardPage, \"wowyUrtpVAxMhALd0EpbGvQYaWY=\");\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/leaderboard/page.tsx\n"));

/***/ })

});