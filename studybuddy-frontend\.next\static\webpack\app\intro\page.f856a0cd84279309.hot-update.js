"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/intro/page",{

/***/ "(app-pages-browser)/./src/lib/api/quiz.ts":
/*!*****************************!*\
  !*** ./src/lib/api/quiz.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quizApi: () => (/* binding */ quizApi),\n/* harmony export */   subjectApi: () => (/* binding */ subjectApi)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const token = localStorage.getItem('accessToken');\n    return {\n        'Authorization': \"Bearer \".concat(token),\n        'Content-Type': 'application/json'\n    };\n};\n// Subject API functions\nconst subjectApi = {\n    getAll: async ()=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/users/subjects\"), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to fetch subjects');\n        }\n        return response.json();\n    },\n    getById: async (id)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/users/subjects/\").concat(id), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to fetch subject');\n        }\n        return response.json();\n    }\n};\n// Quiz API functions\nconst quizApi = {\n    getAll: async (filter)=>{\n        const params = new URLSearchParams();\n        if (filter === null || filter === void 0 ? void 0 : filter.subjectId) params.append('subjectId', filter.subjectId);\n        if (filter === null || filter === void 0 ? void 0 : filter.topicId) params.append('topicId', filter.topicId);\n        if (filter === null || filter === void 0 ? void 0 : filter.noOfQuestions) params.append('noOfQuestions', filter.noOfQuestions.toString());\n        // Try user-facing endpoint first, fallback to admin endpoint\n        let url = \"\".concat(API_BASE_URL, \"/users/quizzes\").concat(params.toString() ? \"?\".concat(params.toString()) : '');\n        let response = await fetch(url, {\n            headers: getAuthHeaders()\n        });\n        // If user endpoint doesn't exist, try admin endpoint\n        if (!response.ok && response.status === 404) {\n            url = \"\".concat(API_BASE_URL, \"/admin/quizzes\").concat(params.toString() ? \"?\".concat(params.toString()) : '');\n            response = await fetch(url, {\n                headers: getAuthHeaders()\n            });\n        }\n        if (!response.ok) {\n            throw new Error('Failed to fetch quizzes');\n        }\n        return response.json();\n    },\n    getById: async (id)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes/\").concat(id), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to fetch quiz');\n        }\n        return response.json();\n    },\n    create: async (data)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes\"), {\n            method: 'POST',\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to create quiz');\n        }\n        return response.json();\n    },\n    update: async (id, data)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes/\").concat(id), {\n            method: 'PUT',\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to update quiz');\n        }\n        return response.json();\n    },\n    delete: async (id)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes/\").concat(id), {\n            method: 'DELETE',\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to delete quiz');\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/quiz.ts\n"));

/***/ })

});