"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/subject-card */ \"(app-pages-browser)/./src/components/dashboard/subject-card.tsx\");\n/* harmony import */ var _components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/recommendation-card */ \"(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(app-pages-browser)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst subjectColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-red-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-yellow-500\"\n];\nfunction Dashboard() {\n    _s();\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            loadSubjects();\n            loadRecommendations();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const loadSubjects = async ()=>{\n        try {\n            setLoading(true);\n            const apiSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            const localSubjects = apiSubjects.map((subject, index)=>({\n                    id: subject._id,\n                    subject: subject.name,\n                    color: subjectColors[index % subjectColors.length],\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }));\n            setSubjects(localSubjects);\n        } catch (error) {\n            console.error('Failed to load subjects:', error);\n            // Fallback to default subjects\n            setSubjects([\n                {\n                    id: \"1\",\n                    subject: \"Maths\",\n                    color: \"bg-blue-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"2\",\n                    subject: \"Physics\",\n                    color: \"bg-orange-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"3\",\n                    subject: \"Biology\",\n                    color: \"bg-red-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"4\",\n                    subject: \"Chemistry\",\n                    color: \"bg-green-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRecommendations = async ()=>{\n        // For now, use static recommendations\n        // In a real app, this would fetch user's quiz history and generate recommendations\n        setRecommendations([\n            {\n                subject: \"Algebra\",\n                score: \"6/10\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Algebra\",\n                score: \"6/10\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Velocity\",\n                score: \"6/10\",\n                color: \"bg-orange-50\",\n                buttonColor: \"bg-orange-500\"\n            },\n            {\n                subject: \"Evolution\",\n                score: \"6/10\",\n                color: \"bg-red-50\",\n                buttonColor: \"bg-red-500\"\n            },\n            {\n                subject: \"Evolution\",\n                score: \"6/10\",\n                color: \"bg-red-50\",\n                buttonColor: \"bg-red-500\"\n            },\n            {\n                subject: \"Organic Theory\",\n                score: \"6/10\",\n                color: \"bg-green-50\",\n                buttonColor: \"bg-green-500\"\n            },\n            {\n                subject: \"Algebra\",\n                score: \"6/10\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Velocity\",\n                score: \"6/10\",\n                color: \"bg-orange-50\",\n                buttonColor: \"bg-orange-500\"\n            }\n        ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__.AppSidebar, {\n                currentPage: \"dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-medium text-gray-800 mb-4\",\n                                    children: \"Welcome back, Student \\uD83D\\uDC4B\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"Continue Learning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: subjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__.SubjectCard, {\n                                                subject: subject.subject,\n                                                color: subject.color,\n                                                image: subject.image,\n                                                subjectId: subject.id\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Smart Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__.RecommendationCard, {\n                                                subject: rec.subject,\n                                                score: rec.score,\n                                                color: rec.color,\n                                                buttonColor: rec.buttonColor\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"57SnyBaWgXr6VpFPMMScMw81FKI=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});