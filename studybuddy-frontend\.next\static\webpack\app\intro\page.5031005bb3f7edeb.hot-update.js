"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/intro/page",{

/***/ "(app-pages-browser)/./src/app/intro/page.tsx":
/*!********************************!*\
  !*** ./src/app/intro/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OnboardingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_onboarding_login__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/onboarding/login */ \"(app-pages-browser)/./src/components/onboarding/login.tsx\");\n/* harmony import */ var _components_onboarding_step_1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/onboarding/step-1 */ \"(app-pages-browser)/./src/components/onboarding/step-1.tsx\");\n/* harmony import */ var _components_onboarding_profile_setup__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/onboarding/profile-setup */ \"(app-pages-browser)/./src/components/onboarding/profile-setup.tsx\");\n/* harmony import */ var _components_onboarding_step_2__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/onboarding/step-2 */ \"(app-pages-browser)/./src/components/onboarding/step-2.tsx\");\n/* harmony import */ var _components_onboarding_step_3__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/onboarding/step-3 */ \"(app-pages-browser)/./src/components/onboarding/step-3.tsx\");\n/* harmony import */ var _components_onboarding_step_4__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/onboarding/step-4 */ \"(app-pages-browser)/./src/components/onboarding/step-4.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction OnboardingPage() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // Start with login (step 0)\n    ;\n    const nextStep = ()=>{\n        if (currentStep < 5) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 0) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const skipIntro = ()=>{\n        setCurrentStep(5) // Skip to final step\n        ;\n    };\n    const renderStep = ()=>{\n        switch(currentStep){\n            case 0:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_login__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onNext: nextStep,\n                    onSkip: skipIntro\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_1__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onNext: nextStep,\n                    onSkip: skipIntro\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 16\n                }, this);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_profile_setup__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onNext: nextStep,\n                    onPrev: prevStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_2__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    onNext: nextStep,\n                    onPrev: prevStep,\n                    onSkip: skipIntro\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 16\n                }, this);\n            case 4:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_3__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    onNext: nextStep,\n                    onPrev: prevStep,\n                    onSkip: skipIntro\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case 5:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_step_4__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    onPrev: prevStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_login__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onNext: nextStep,\n                    onSkip: skipIntro\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: renderStep()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\intro\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 10\n    }, this);\n}\n_s(OnboardingPage, \"1sJm2lQ2mRX7Y0EEARB7TDldOEM=\");\n_c = OnboardingPage;\nvar _c;\n$RefreshReg$(_c, \"OnboardingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvaW50cm8vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRWdDO0FBRXFCO0FBQ087QUFDSTtBQUNKO0FBQ0E7QUFDQTtBQUU3QyxTQUFTTzs7SUFDdEIsTUFBTSxDQUFDQyxhQUFhQyxlQUFlLEdBQUdULCtDQUFRQSxDQUFDLEdBQUcsNEJBQTRCOztJQUU5RSxNQUFNVSxXQUFXO1FBQ2YsSUFBSUYsY0FBYyxHQUFHO1lBQ25CQyxlQUFlRCxjQUFjO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNRyxXQUFXO1FBQ2YsSUFBSUgsY0FBYyxHQUFHO1lBQ25CQyxlQUFlRCxjQUFjO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNSSxZQUFZO1FBQ2hCSCxlQUFlLEdBQUcscUJBQXFCOztJQUN6QztJQUVBLE1BQU1JLGFBQWE7UUFDakIsT0FBUUw7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDUCxvRUFBU0E7b0JBQUNhLFFBQVFKO29CQUFVSyxRQUFRSDs7Ozs7O1lBQzlDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNWLHFFQUFlQTtvQkFBQ1ksUUFBUUo7b0JBQVVLLFFBQVFIOzs7Ozs7WUFDcEQsS0FBSztnQkFDSCxxQkFBTyw4REFBQ1QsNEVBQVlBO29CQUFDVyxRQUFRSjtvQkFBVU0sUUFBUUw7Ozs7OztZQUNqRCxLQUFLO2dCQUNILHFCQUFPLDhEQUFDUCxxRUFBZUE7b0JBQUNVLFFBQVFKO29CQUFVTSxRQUFRTDtvQkFBVUksUUFBUUg7Ozs7OztZQUN0RSxLQUFLO2dCQUNILHFCQUFPLDhEQUFDUCxxRUFBZUE7b0JBQUNTLFFBQVFKO29CQUFVTSxRQUFRTDtvQkFBVUksUUFBUUg7Ozs7OztZQUN0RSxLQUFLO2dCQUNILHFCQUFPLDhEQUFDTixxRUFBZUE7b0JBQUNVLFFBQVFMOzs7Ozs7WUFDbEM7Z0JBQ0UscUJBQU8sOERBQUNWLG9FQUFTQTtvQkFBQ2EsUUFBUUo7b0JBQVVLLFFBQVFIOzs7Ozs7UUFDaEQ7SUFDRjtJQUVBLHFCQUFPLDhEQUFDSztRQUFJQyxXQUFVO2tCQUEyQkw7Ozs7OztBQUNuRDtHQXZDd0JOO0tBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFkYXJzXFxEZXNrdG9wXFxGTFxcVmVsb2NpdHlcXHN0dWR5YnVkZHktZnJvbnRlbmRcXHNyY1xcYXBwXFxpbnRyb1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiXHJcbmltcG9ydCBMb2dpblBhZ2UgZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL2xvZ2luXCJcclxuaW1wb3J0IE9uYm9hcmRpbmdTdGVwMSBmcm9tIFwiQC9jb21wb25lbnRzL29uYm9hcmRpbmcvc3RlcC0xXCJcclxuaW1wb3J0IFByb2ZpbGVTZXR1cCBmcm9tIFwiQC9jb21wb25lbnRzL29uYm9hcmRpbmcvcHJvZmlsZS1zZXR1cFwiXHJcbmltcG9ydCBPbmJvYXJkaW5nU3RlcDIgZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL3N0ZXAtMlwiXHJcbmltcG9ydCBPbmJvYXJkaW5nU3RlcDMgZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL3N0ZXAtM1wiXHJcbmltcG9ydCBPbmJvYXJkaW5nU3RlcDQgZnJvbSBcIkAvY29tcG9uZW50cy9vbmJvYXJkaW5nL3N0ZXAtNFwiXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBPbmJvYXJkaW5nUGFnZSgpIHtcclxuICBjb25zdCBbY3VycmVudFN0ZXAsIHNldEN1cnJlbnRTdGVwXSA9IHVzZVN0YXRlKDApIC8vIFN0YXJ0IHdpdGggbG9naW4gKHN0ZXAgMClcclxuXHJcbiAgY29uc3QgbmV4dFN0ZXAgPSAoKSA9PiB7XHJcbiAgICBpZiAoY3VycmVudFN0ZXAgPCA1KSB7XHJcbiAgICAgIHNldEN1cnJlbnRTdGVwKGN1cnJlbnRTdGVwICsgMSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IHByZXZTdGVwID0gKCkgPT4ge1xyXG4gICAgaWYgKGN1cnJlbnRTdGVwID4gMCkge1xyXG4gICAgICBzZXRDdXJyZW50U3RlcChjdXJyZW50U3RlcCAtIDEpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBza2lwSW50cm8gPSAoKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50U3RlcCg1KSAvLyBTa2lwIHRvIGZpbmFsIHN0ZXBcclxuICB9XHJcblxyXG4gIGNvbnN0IHJlbmRlclN0ZXAgPSAoKSA9PiB7XHJcbiAgICBzd2l0Y2ggKGN1cnJlbnRTdGVwKSB7XHJcbiAgICAgIGNhc2UgMDpcclxuICAgICAgICByZXR1cm4gPExvZ2luUGFnZSBvbk5leHQ9e25leHRTdGVwfSBvblNraXA9e3NraXBJbnRyb30gLz5cclxuICAgICAgY2FzZSAxOlxyXG4gICAgICAgIHJldHVybiA8T25ib2FyZGluZ1N0ZXAxIG9uTmV4dD17bmV4dFN0ZXB9IG9uU2tpcD17c2tpcEludHJvfSAvPlxyXG4gICAgICBjYXNlIDI6XHJcbiAgICAgICAgcmV0dXJuIDxQcm9maWxlU2V0dXAgb25OZXh0PXtuZXh0U3RlcH0gb25QcmV2PXtwcmV2U3RlcH0gLz5cclxuICAgICAgY2FzZSAzOlxyXG4gICAgICAgIHJldHVybiA8T25ib2FyZGluZ1N0ZXAyIG9uTmV4dD17bmV4dFN0ZXB9IG9uUHJldj17cHJldlN0ZXB9IG9uU2tpcD17c2tpcEludHJvfSAvPlxyXG4gICAgICBjYXNlIDQ6XHJcbiAgICAgICAgcmV0dXJuIDxPbmJvYXJkaW5nU3RlcDMgb25OZXh0PXtuZXh0U3RlcH0gb25QcmV2PXtwcmV2U3RlcH0gb25Ta2lwPXtza2lwSW50cm99IC8+XHJcbiAgICAgIGNhc2UgNTpcclxuICAgICAgICByZXR1cm4gPE9uYm9hcmRpbmdTdGVwNCBvblByZXY9e3ByZXZTdGVwfSAvPlxyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHJldHVybiA8TG9naW5QYWdlIG9uTmV4dD17bmV4dFN0ZXB9IG9uU2tpcD17c2tpcEludHJvfSAvPlxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj57cmVuZGVyU3RlcCgpfTwvZGl2PlxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkxvZ2luUGFnZSIsIk9uYm9hcmRpbmdTdGVwMSIsIlByb2ZpbGVTZXR1cCIsIk9uYm9hcmRpbmdTdGVwMiIsIk9uYm9hcmRpbmdTdGVwMyIsIk9uYm9hcmRpbmdTdGVwNCIsIk9uYm9hcmRpbmdQYWdlIiwiY3VycmVudFN0ZXAiLCJzZXRDdXJyZW50U3RlcCIsIm5leHRTdGVwIiwicHJldlN0ZXAiLCJza2lwSW50cm8iLCJyZW5kZXJTdGVwIiwib25OZXh0Iiwib25Ta2lwIiwib25QcmV2IiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/intro/page.tsx\n"));

/***/ })

});