"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leaderboard/page",{

/***/ "(app-pages-browser)/./src/lib/api/leaderboard.ts":
/*!************************************!*\
  !*** ./src/lib/api/leaderboard.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLASS_OPTIONS: () => (/* binding */ CLASS_OPTIONS),\n/* harmony export */   LeaderboardAPI: () => (/* binding */ LeaderboardAPI),\n/* harmony export */   SUBJECT_OPTIONS: () => (/* binding */ SUBJECT_OPTIONS),\n/* harmony export */   formatSparkPoints: () => (/* binding */ formatSparkPoints),\n/* harmony export */   getPositionColor: () => (/* binding */ getPositionColor),\n/* harmony export */   getPositionSuffix: () => (/* binding */ getPositionSuffix)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nclass LeaderboardAPI {\n    static getAuthHeaders() {\n        const token = localStorage.getItem('accessToken');\n        return {\n            'Content-Type': 'application/json',\n            ...token && {\n                Authorization: \"Bearer \".concat(token)\n            }\n        };\n    }\n    static buildQueryParams(filters) {\n        const params = new URLSearchParams();\n        if (filters.period) params.append('period', filters.period);\n        if (filters.subject) params.append('subject', filters.subject);\n        if (filters.class) params.append('class', filters.class);\n        if (filters.limit) params.append('limit', filters.limit.toString());\n        return params.toString();\n    }\n    static async getLeaderboard() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const queryParams = this.buildQueryParams(filters);\n        const url = \"\".concat(API_BASE_URL, \"/leaderboard\").concat(queryParams ? \"?\".concat(queryParams) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch leaderboard: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    static async getUserRank() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const queryParams = this.buildQueryParams(filters);\n        const url = \"\".concat(API_BASE_URL, \"/leaderboard/user-rank\").concat(queryParams ? \"?\".concat(queryParams) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch user rank: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    static async searchUsers(searchQuery) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const params = new URLSearchParams({\n            query: searchQuery\n        });\n        if (filters.period) params.append('period', filters.period);\n        if (filters.subject) params.append('subject', filters.subject);\n        if (filters.class) params.append('class', filters.class);\n        const url = \"\".concat(API_BASE_URL, \"/leaderboard/search?\").concat(params.toString());\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to search users: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n    static async getTopPerformers() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const queryParams = this.buildQueryParams(filters);\n        const url = \"\".concat(API_BASE_URL, \"/leaderboard/top-performers\").concat(queryParams ? \"?\".concat(queryParams) : '');\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: this.getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch top performers: \".concat(response.statusText));\n        }\n        return response.json();\n    }\n}\n// Helper functions for formatting\nconst formatSparkPoints = (points)=>{\n    if (points >= 1000) {\n        return \"\".concat((points / 1000).toFixed(1), \"k\");\n    }\n    return points.toString();\n};\nconst getPositionSuffix = (position)=>{\n    const lastDigit = position % 10;\n    const lastTwoDigits = position % 100;\n    if (lastTwoDigits >= 11 && lastTwoDigits <= 13) {\n        return 'th';\n    }\n    switch(lastDigit){\n        case 1:\n            return 'st';\n        case 2:\n            return 'nd';\n        case 3:\n            return 'rd';\n        default:\n            return 'th';\n    }\n};\nconst getPositionColor = (position)=>{\n    switch(position){\n        case 1:\n            return 'bg-yellow-500';\n        case 2:\n            return 'bg-blue-500';\n        case 3:\n            return 'bg-orange-500';\n        default:\n            return 'bg-gray-400';\n    }\n};\n// Subject options for filtering\nconst SUBJECT_OPTIONS = [\n    'Mathematics',\n    'Physics',\n    'Chemistry',\n    'Biology',\n    'English',\n    'Computer Science',\n    'Economics',\n    'History',\n    'Geography',\n    'Political Science'\n];\n// Class options for filtering\nconst CLASS_OPTIONS = [\n    'Class 9',\n    'Class 10',\n    'Class 11',\n    'Class 12',\n    'PUC 1',\n    'PUC 2'\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/leaderboard.ts\n"));

/***/ })

});