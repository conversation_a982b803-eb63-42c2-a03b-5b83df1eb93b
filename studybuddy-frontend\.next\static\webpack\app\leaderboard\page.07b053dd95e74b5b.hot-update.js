"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leaderboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/app-sidebar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Lightbulb,LogOut,Trophy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction AppSidebar(param) {\n    let { currentPage = \"dashboard\" } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const handleLogout = ()=>{\n        try {\n            // Clear all items from localStorage\n            localStorage.clear();\n            // Show success toast\n            toast({\n                title: \"Logged out successfully\",\n                description: \"You have been logged out of your account\"\n            });\n            // Redirect to intro page with LoginPage component\n            router.push('/intro');\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to log out. Please try again.\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n        className: \"border-r border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarHeader, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-800\",\n                            children: \"study\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-blue-500\",\n                            children: \"buddy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroup, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupLabel, {\n                            className: \"text-gray-500 text-sm font-medium mb-4\",\n                            children: \"Main Menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarGroupContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                            asChild: true,\n                                            isActive: currentPage === \"dashboard\",\n                                            className: currentPage === \"dashboard\" ? \"bg-blue-500 text-white hover:bg-blue-600 rounded-full\" : \"text-gray-600 hover:text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/dashboard\",\n                                                className: \"flex items-center gap-3 px-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"My Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuItem, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarMenuButton, {\n                                            asChild: true,\n                                            isActive: currentPage === \"dashboard/leaderboard\",\n                                            className: currentPage === \"dashboard/leaderboard\" ? \"bg-blue-500 text-white hover:bg-blue-600 rounded-full\" : \"text-gray-600 hover:text-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/leaderboard\",\n                                                className: \"flex items-center gap-3 px-4 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Leaderboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarFooter, {\n                className: \"p-4 space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-orange-50 border border-orange-200 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 text-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-orange-700\",\n                                        children: \"More features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-orange-600\",\n                                children: \"Coming soon!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleLogout,\n                        className: \"flex items-center gap-2 w-full p-3 text-red-500 bg-red-50 rounded-lg hover:bg-red-100 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Lightbulb_LogOut_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\app-sidebar.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"ghMoU9cDQgqp28oHl3zuUPzX2DY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\n"));

/***/ })

});