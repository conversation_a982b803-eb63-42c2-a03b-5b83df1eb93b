"use client"

import { useState, useEffect } from "react"
import { SubjectCard } from "@/components/dashboard/subject-card"
import { RecommendationCard } from "@/components/dashboard/recommendation-card"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { AppSidebar } from "@/components/dashboard/app-sidebar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { Zap } from "lucide-react"
import { subjectApi, Subject } from "@/lib/api/quiz"

interface LocalSubject {
  subject: string
  color: string
  image: string
  id: string
}

interface QuizRecommendation {
  subject: string
  score: string
  color: string
  buttonColor: string
  subjectId?: string
  topicId?: string
}

const subjectColors = ["bg-blue-500", "bg-orange-500", "bg-red-500", "bg-green-500", "bg-purple-500", "bg-yellow-500"]

export default function Dashboard() {
  const [subjects, setSubjects] = useState<LocalSubject[]>([])
  const [recommendations, setRecommendations] = useState<QuizRecommendation[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadSubjects()
    loadRecommendations()
  }, [])

  const loadSubjects = async () => {
    try {
      setLoading(true)
      const apiSubjects = await subjectApi.getAll()

      const localSubjects: LocalSubject[] = apiSubjects.map((subject, index) => ({
        id: subject._id,
        subject: subject.name,
        color: subjectColors[index % subjectColors.length],
        image: "/placeholder.svg?height=64&width=64"
      }))

      setSubjects(localSubjects)
    } catch (error) {
      console.error('Failed to load subjects:', error)
      // Fallback to default subjects
      setSubjects([
        { id: "1", subject: "Maths", color: "bg-blue-500", image: "/placeholder.svg?height=64&width=64" },
        { id: "2", subject: "Physics", color: "bg-orange-500", image: "/placeholder.svg?height=64&width=64" },
        { id: "3", subject: "Biology", color: "bg-red-500", image: "/placeholder.svg?height=64&width=64" },
        { id: "4", subject: "Chemistry", color: "bg-green-500", image: "/placeholder.svg?height=64&width=64" },
      ])
    } finally {
      setLoading(false)
    }
  }

  const loadRecommendations = async () => {
    // For now, use static recommendations
    // In a real app, this would fetch user's quiz history and generate recommendations
    setRecommendations([
      { subject: "Algebra", score: "6/10", color: "bg-blue-50", buttonColor: "bg-blue-500" },
      { subject: "Algebra", score: "6/10", color: "bg-blue-50", buttonColor: "bg-blue-500" },
      { subject: "Velocity", score: "6/10", color: "bg-orange-50", buttonColor: "bg-orange-500" },
      { subject: "Evolution", score: "6/10", color: "bg-red-50", buttonColor: "bg-red-500" },
      { subject: "Evolution", score: "6/10", color: "bg-red-50", buttonColor: "bg-red-500" },
      { subject: "Organic Theory", score: "6/10", color: "bg-green-50", buttonColor: "bg-green-500" },
      { subject: "Algebra", score: "6/10", color: "bg-blue-50", buttonColor: "bg-blue-500" },
      { subject: "Velocity", score: "6/10", color: "bg-orange-50", buttonColor: "bg-orange-500" },
    ])
  }
  return (
    <SidebarProvider>
      <AppSidebar currentPage="dashboard" />
      <SidebarInset>
        <DashboardHeader />

        <main className="flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen">
          {/* Mobile Welcome Message */}
          <div className="md:hidden">
            <h1 className="text-xl font-medium text-gray-800 mb-4">Welcome back, Student 👋</h1>
          </div>

          {/* Continue Learning Section */}
          <section>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Continue Learning</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {subjects.map((subject, index) => (
                <SubjectCard
                  key={index}
                  subject={subject.subject}
                  color={subject.color}
                  image={subject.image}
                  subjectId={subject.id}
                />
              ))}
            </div>
          </section>

          {/* Smart Recommendations Section */}
          <section>
            <div className="flex items-center gap-2 mb-4">
              <Zap className="h-5 w-5 text-blue-500" />
              <h2 className="text-lg font-semibold text-gray-800">Smart Recommendations</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {recommendations.map((rec, index) => (
                <RecommendationCard
                  key={index}
                  subject={rec.subject}
                  score={rec.score}
                  color={rec.color}
                  buttonColor={rec.buttonColor}
                />
              ))}
            </div>
          </section>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
