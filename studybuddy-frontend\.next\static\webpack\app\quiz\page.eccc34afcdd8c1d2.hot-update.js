"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/quiz/page",{

/***/ "(app-pages-browser)/./src/app/quiz/page.tsx":
/*!*******************************!*\
  !*** ./src/app/quiz/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AlgebraQuiz)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* harmony import */ var _components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/raise-issue-modal */ \"(app-pages-browser)/./src/components/ui/raise-issue-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst sampleQuestions = [\n    {\n        id: 1,\n        question: \"What is the value of x in the equation 3x + 7 = 22?\",\n        options: [\n            \"x = 5\",\n            \"x = 3\",\n            \"x = 4\",\n            \"x = 7\"\n        ],\n        correctAnswer: 0,\n        explanation: \"To solve 3x + 7 = 22, subtract 7 from both sides: 3x = 15, then divide by 3: x = 5\"\n    },\n    {\n        id: 2,\n        question: \"Simplify: 2x + 3x - x\",\n        options: [\n            \"4x\",\n            \"5x\",\n            \"6x\",\n            \"3x\"\n        ],\n        correctAnswer: 0,\n        explanation: \"Combine like terms: 2x + 3x - x = (2 + 3 - 1)x = 4x\"\n    },\n    {\n        id: 3,\n        question: \"What is the slope of the line y = 2x + 5?\",\n        options: [\n            \"2\",\n            \"5\",\n            \"-2\",\n            \"1/2\"\n        ],\n        correctAnswer: 0,\n        explanation: \"In the form y = mx + b, m is the slope. Here m = 2\"\n    },\n    {\n        id: 4,\n        question: \"Factor: x² - 9\",\n        options: [\n            \"(x - 3)(x + 3)\",\n            \"(x - 9)(x + 1)\",\n            \"(x - 3)²\",\n            \"Cannot be factored\"\n        ],\n        correctAnswer: 0,\n        explanation: \"This is a difference of squares: x² - 9 = x² - 3² = (x - 3)(x + 3)\"\n    },\n    {\n        id: 5,\n        question: \"Solve for y: 2y - 6 = 10\",\n        options: [\n            \"y = 8\",\n            \"y = 2\",\n            \"y = 4\",\n            \"y = 6\"\n        ],\n        correctAnswer: 0,\n        explanation: \"Add 6 to both sides: 2y = 16, then divide by 2: y = 8\"\n    },\n    {\n        id: 6,\n        question: \"What is the y-intercept of y = -3x + 7?\",\n        options: [\n            \"7\",\n            \"-3\",\n            \"3\",\n            \"-7\"\n        ],\n        correctAnswer: 0,\n        explanation: \"In the form y = mx + b, b is the y-intercept. Here b = 7\"\n    },\n    {\n        id: 7,\n        question: \"Expand: (x + 2)(x + 3)\",\n        options: [\n            \"x² + 5x + 6\",\n            \"x² + 6x + 5\",\n            \"x² + 5x + 5\",\n            \"x² + 6x + 6\"\n        ],\n        correctAnswer: 0,\n        explanation: \"Use FOIL: (x + 2)(x + 3) = x² + 3x + 2x + 6 = x² + 5x + 6\"\n    },\n    {\n        id: 8,\n        question: \"If 4x - 8 = 12, what is x?\",\n        options: [\n            \"x = 5\",\n            \"x = 3\",\n            \"x = 4\",\n            \"x = 1\"\n        ],\n        correctAnswer: 0,\n        explanation: \"Add 8 to both sides: 4x = 20, then divide by 4: x = 5\"\n    },\n    {\n        id: 9,\n        question: \"What is the vertex of y = x² - 4x + 3?\",\n        options: [\n            \"(2, -1)\",\n            \"(1, 0)\",\n            \"(3, 0)\",\n            \"(2, 1)\"\n        ],\n        correctAnswer: 0,\n        explanation: \"For y = ax² + bx + c, vertex x = -b/2a = -(-4)/2(1) = 2. When x = 2: y = 4 - 8 + 3 = -1\"\n    },\n    {\n        id: 10,\n        question: \"Solve: x² - 5x + 6 = 0\",\n        options: [\n            \"x = 2, 3\",\n            \"x = 1, 6\",\n            \"x = -2, -3\",\n            \"x = 5, 1\"\n        ],\n        correctAnswer: 0,\n        explanation: \"Factor: (x - 2)(x - 3) = 0, so x = 2 or x = 3\"\n    }\n];\nfunction AlgebraQuiz() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const [currentScreen, setCurrentScreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"setup\");\n    const [quizSettings, setQuizSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        difficulty: \"\",\n        numQuestions: 10,\n        questionType: \"\"\n    });\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedAnswer, setSelectedAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userAnswers, setUserAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [timeRemaining, setTimeRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(300) // 5 minutes\n    ;\n    const [quizQuestions, setQuizQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [expandedQuestions, setExpandedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Context from URL parameters\n    const [subjectId, setSubjectId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [topicId, setTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subjectName, setSubjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [topicName, setTopicName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRaiseIssueModal, setShowRaiseIssueModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle URL parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AlgebraQuiz.useEffect\": ()=>{\n            const subjectFromUrl = searchParams.get('subject');\n            const topicFromUrl = searchParams.get('topic');\n            const subjectNameFromUrl = searchParams.get('subjectName');\n            const topicNameFromUrl = searchParams.get('topicName');\n            if (subjectFromUrl) setSubjectId(subjectFromUrl);\n            if (topicFromUrl) setTopicId(topicFromUrl);\n            if (subjectNameFromUrl) setSubjectName(decodeURIComponent(subjectNameFromUrl));\n            if (topicNameFromUrl) setTopicName(decodeURIComponent(topicNameFromUrl));\n        }\n    }[\"AlgebraQuiz.useEffect\"], [\n        searchParams\n    ]);\n    // Timer effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AlgebraQuiz.useEffect\": ()=>{\n            if (currentScreen === \"quiz\" && timeRemaining > 0) {\n                const timer = setInterval({\n                    \"AlgebraQuiz.useEffect.timer\": ()=>{\n                        setTimeRemaining({\n                            \"AlgebraQuiz.useEffect.timer\": (prev)=>prev - 1\n                        }[\"AlgebraQuiz.useEffect.timer\"]);\n                    }\n                }[\"AlgebraQuiz.useEffect.timer\"], 1000);\n                return ({\n                    \"AlgebraQuiz.useEffect\": ()=>clearInterval(timer)\n                })[\"AlgebraQuiz.useEffect\"];\n            } else if (timeRemaining === 0 && currentScreen === \"quiz\") {\n                handleFinishQuiz();\n            }\n        }\n    }[\"AlgebraQuiz.useEffect\"], [\n        currentScreen,\n        timeRemaining\n    ]);\n    const formatTime = (seconds)=>{\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        const secs = seconds % 60;\n        return \"\".concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(secs.toString().padStart(2, \"0\"));\n    };\n    const startQuiz = async ()=>{\n        if (!quizSettings.difficulty || !quizSettings.questionType) return;\n        try {\n            setLoading(true);\n            // Try to fetch questions from API\n            let apiQuestions = [];\n            // First try with subject and topic if available\n            if (subjectId && topicId) {\n                try {\n                    console.log('Fetching quizzes for subject:', subjectId, 'topic:', topicId);\n                    const quizzes = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.quizApi.getAll({\n                        subjectId,\n                        topicId,\n                        noOfQuestions: quizSettings.numQuestions\n                    });\n                    if (quizzes && quizzes.length > 0) {\n                        console.log('Found', quizzes.length, 'API quizzes');\n                        apiQuestions = quizzes.map((quiz, index)=>({\n                                id: index + 1,\n                                question: quiz.question,\n                                options: quiz.options.map((opt)=>opt.text),\n                                correctAnswer: quiz.options.findIndex((opt)=>opt.isCorrect),\n                                explanation: quiz.explanation || \"\"\n                            }));\n                    }\n                } catch (error) {\n                    console.log('Failed to fetch quizzes with subject/topic:', error);\n                }\n            }\n            // If no questions found with subject/topic, try without filters\n            if (apiQuestions.length === 0) {\n                try {\n                    console.log('Trying to fetch general quizzes');\n                    const quizzes = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.quizApi.getAll({\n                        noOfQuestions: quizSettings.numQuestions\n                    });\n                    if (quizzes && quizzes.length > 0) {\n                        console.log('Found', quizzes.length, 'general API quizzes');\n                        apiQuestions = quizzes.map((quiz, index)=>({\n                                id: index + 1,\n                                question: quiz.question,\n                                options: quiz.options.map((opt)=>opt.text),\n                                correctAnswer: quiz.options.findIndex((opt)=>opt.isCorrect),\n                                explanation: quiz.explanation || \"\"\n                            }));\n                    }\n                } catch (error) {\n                    console.log('Failed to fetch general quizzes, using sample questions:', error);\n                }\n            }\n            // Use API questions if available, otherwise fall back to sample questions\n            const questionsToUse = apiQuestions.length > 0 ? apiQuestions : sampleQuestions;\n            const shuffledQuestions = [\n                ...questionsToUse\n            ].sort(()=>Math.random() - 0.5).slice(0, quizSettings.numQuestions);\n            setQuizQuestions(shuffledQuestions);\n            setCurrentScreen(\"quiz\");\n            setTimeRemaining(quizSettings.numQuestions * 30) // 30 seconds per question\n            ;\n        } catch (error) {\n            console.error('Error starting quiz:', error);\n            alert('Failed to start quiz. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAnswerSelect = (answerIndex)=>{\n        setSelectedAnswer(answerIndex);\n    };\n    const handleNextQuestion = ()=>{\n        if (selectedAnswer === null) return;\n        const currentQuestion = quizQuestions[currentQuestionIndex];\n        const isCorrect = selectedAnswer === currentQuestion.correctAnswer;\n        setUserAnswers((prev)=>[\n                ...prev,\n                {\n                    questionId: currentQuestion.id,\n                    selectedAnswer,\n                    isCorrect\n                }\n            ]);\n        if (currentQuestionIndex < quizQuestions.length - 1) {\n            setCurrentQuestionIndex((prev)=>prev + 1);\n            setSelectedAnswer(null);\n        } else {\n            handleFinishQuiz();\n        }\n    };\n    const handleFinishQuiz = ()=>{\n        setCurrentScreen(\"results\");\n    };\n    const getScore = ()=>{\n        return userAnswers.filter((answer)=>answer.isCorrect).length;\n    };\n    const getScoreMessage = ()=>{\n        const score = getScore();\n        const percentage = score / quizQuestions.length * 100;\n        if (percentage >= 80) return \"🎉 You did it!\";\n        if (percentage >= 60) return \"Nice effort!\";\n        if (percentage >= 40) return \"Keep practicing!\";\n        return \"Don't give up!\";\n    };\n    const toggleQuestionExpansion = (questionId)=>{\n        setExpandedQuestions((prev)=>prev.includes(questionId) ? prev.filter((id)=>id !== questionId) : [\n                ...prev,\n                questionId\n            ]);\n    };\n    const resetQuiz = ()=>{\n        setCurrentScreen(\"setup\");\n        setCurrentQuestionIndex(0);\n        setSelectedAnswer(null);\n        setUserAnswers([]);\n        setQuizQuestions([]);\n        setExpandedQuestions([]);\n        setQuizSettings({\n            difficulty: \"\",\n            numQuestions: 10,\n            questionType: \"\"\n        });\n    };\n    if (currentScreen === \"setup\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white p-4 md:p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto border-2 rounded-2xl p-6 md:p-8 min-h-[calc(100vh-2rem)]\",\n                style: {\n                    borderColor: \"#309CEC\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-medium\",\n                                style: {\n                                    color: \"#309CEC\"\n                                },\n                                children: [\n                                    \"Topic: \",\n                                    topicName || \"Algebra\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                className: \"px-6 py-2 rounded-full border-red-200 text-red-500 hover:bg-red-50\",\n                                onClick: ()=>router.push('/dashboard'),\n                                children: \"Exit Quiz\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-8\",\n                                children: \"Customize Your Quiz\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 max-w-lg mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                        value: quizSettings.difficulty,\n                                        onValueChange: (value)=>setQuizSettings((prev)=>({\n                                                    ...prev,\n                                                    difficulty: value\n                                                })),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                className: \"h-12 rounded-xl border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                    placeholder: \"Choose Difficulty Level\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                        value: \"easy\",\n                                                        children: \"Easy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                        value: \"medium\",\n                                                        children: \"Medium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                        value: \"hard\",\n                                                        children: \"Hard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"number\",\n                                        placeholder: \"Number of Questions\",\n                                        value: quizSettings.numQuestions,\n                                        onChange: (e)=>setQuizSettings((prev)=>({\n                                                    ...prev,\n                                                    numQuestions: Number.parseInt(e.target.value) || 10\n                                                })),\n                                        className: \"h-12 rounded-xl border-gray-200\",\n                                        min: \"1\",\n                                        max: \"10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                        value: quizSettings.questionType,\n                                        onValueChange: (value)=>setQuizSettings((prev)=>({\n                                                    ...prev,\n                                                    questionType: value\n                                                })),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                className: \"h-12 rounded-xl border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                    placeholder: \"Choose Question Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                        value: \"multiple-choice\",\n                                                        children: \"Multiple Choice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                        value: \"true-false\",\n                                                        children: \"True/False\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                        value: \"mixed\",\n                                                        children: \"Mixed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: startQuiz,\n                                        disabled: !quizSettings.difficulty || !quizSettings.questionType,\n                                        className: \"w-full h-12 rounded-xl text-white font-medium\",\n                                        style: {\n                                            backgroundColor: \"#309CEC\"\n                                        },\n                                        children: \"Start Quiz\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, this);\n    }\n    if (currentScreen === \"quiz\") {\n        const currentQuestion = quizQuestions[currentQuestionIndex];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white p-4 md:p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto border-2 rounded-2xl p-6 md:p-8 min-h-[calc(100vh-2rem)]\",\n                style: {\n                    borderColor: \"#309CEC\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-medium\",\n                                style: {\n                                    color: \"#309CEC\"\n                                },\n                                children: \"Topic: Algebra\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                className: \"px-6 py-2 rounded-full border-red-200 text-red-500 hover:bg-red-50\",\n                                onClick: resetQuiz,\n                                children: \"Exit Quiz\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: [\n                                    \"Question \",\n                                    currentQuestionIndex + 1,\n                                    \" of \",\n                                    quizQuestions.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-8\",\n                                children: currentQuestion.question\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 mb-8 max-w-md mx-auto\",\n                                children: currentQuestion.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center p-4 bg-gray-100 rounded-lg cursor-pointer hover:bg-gray-200 transition-colors\",\n                                        onClick: ()=>handleAnswerSelect(index),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4\",\n                                                style: {\n                                                    borderColor: selectedAnswer === index ? \"#309CEC\" : \"#CBD5E1\",\n                                                    backgroundColor: selectedAnswer === index ? \"#309CEC\" : \"transparent\"\n                                                },\n                                                children: selectedAnswer === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-white rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 50\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: option\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: handleNextQuestion,\n                                disabled: selectedAnswer === null,\n                                className: \"w-full max-w-xs h-10 rounded-lg text-white font-medium mb-8\",\n                                style: {\n                                    backgroundColor: \"#309CEC\"\n                                },\n                                children: currentQuestionIndex === quizQuestions.length - 1 ? \"Finish Quiz\" : \"Next Question\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-sm\",\n                                children: [\n                                    \"Time Remaining: \",\n                                    formatTime(timeRemaining)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                lineNumber: 384,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this);\n    }\n    if (currentScreen === \"results\") {\n        const score = getScore();\n        const totalQuestions = quizQuestions.length;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white p-4 md:p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto border-2 rounded-2xl p-6 md:p-8 min-h-[calc(100vh-2rem)]\",\n                    style: {\n                        borderColor: \"#309CEC\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-white text-sm font-medium mb-4\",\n                                    style: {\n                                        backgroundColor: \"#309CEC\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"+\",\n                                        score * 10\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-2\",\n                                    children: [\n                                        \"You scored \",\n                                        score,\n                                        \" out of \",\n                                        totalQuestions\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900 mb-6\",\n                                    children: getScoreMessage()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-8\",\n                                    children: \"Let's review your mistakes to get even better.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 mb-8\",\n                            children: quizQuestions.map((question, index)=>{\n                                const userAnswer = userAnswers.find((a)=>a.questionId === question.id);\n                                const isExpanded = expandedQuestions.includes(question.id);\n                                const wasCorrect = (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.isCorrect) || false;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.Collapsible, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleTrigger, {\n                                            className: \"w-full p-4 text-left border border-gray-200 rounded-lg hover:bg-gray-50 flex justify-between items-center\",\n                                            onClick: ()=>toggleQuestionExpansion(question.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: question.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 35\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 71\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_5__.CollapsibleContent, {\n                                            className: \"border-x border-b border-gray-200 rounded-b-lg p-4 bg-blue-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: [\n                                                            \"The Correct Answer Is: \",\n                                                            question.options[question.correctAnswer]\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !wasCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-600\",\n                                                        children: [\n                                                            \"Your Answer: \",\n                                                            userAnswer ? question.options[userAnswer.selectedAnswer] : \"No answer\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-4 h-4 mt-0.5\",\n                                                                style: {\n                                                                    color: \"#309CEC\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        style: {\n                                                                            color: \"#309CEC\"\n                                                                        },\n                                                                        children: \"Why?\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-700\",\n                                                                        children: question.explanation\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, question.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>router.push('/dashboard'),\n                            className: \"w-full h-12 rounded-xl text-white font-medium\",\n                            style: {\n                                backgroundColor: \"#309CEC\"\n                            },\n                            children: \"Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    isOpen: showRaiseIssueModal,\n                    onClose: ()=>setShowRaiseIssueModal(false),\n                    currentSubject: subjectName,\n                    currentTopic: topicName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\quiz\\\\page.tsx\",\n            lineNumber: 449,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_s(AlgebraQuiz, \"HIYVie+51XAnvvdi+Hj+fmNuBG8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams\n    ];\n});\n_c = AlgebraQuiz;\nvar _c;\n$RefreshReg$(_c, \"AlgebraQuiz\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/quiz/page.tsx\n"));

/***/ })

});