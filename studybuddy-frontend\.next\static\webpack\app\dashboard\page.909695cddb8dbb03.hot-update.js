"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/subject-card */ \"(app-pages-browser)/./src/components/dashboard/subject-card.tsx\");\n/* harmony import */ var _components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/recommendation-card */ \"(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(app-pages-browser)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst subjectColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-red-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-yellow-500\"\n];\nfunction Dashboard() {\n    _s();\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            loadSubjects();\n            loadRecommendations();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const loadSubjects = async ()=>{\n        try {\n            setLoading(true);\n            const apiSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            const localSubjects = apiSubjects.map((subject, index)=>({\n                    id: subject._id,\n                    subject: subject.name,\n                    color: subjectColors[index % subjectColors.length],\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }));\n            setSubjects(localSubjects);\n        } catch (error) {\n            console.error('Failed to load subjects:', error);\n            // Fallback to default subjects\n            setSubjects([\n                {\n                    id: \"1\",\n                    subject: \"Maths\",\n                    color: \"bg-blue-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"2\",\n                    subject: \"Physics\",\n                    color: \"bg-orange-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"3\",\n                    subject: \"Biology\",\n                    color: \"bg-red-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"4\",\n                    subject: \"Chemistry\",\n                    color: \"bg-green-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRecommendations = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch chat history to analyze user's learning patterns\n            const chatResponse = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat/chat-history\"), {\n                headers: getAuthHeaders()\n            });\n            if (!chatResponse.ok) {\n                throw new Error('Failed to fetch chat history');\n            }\n            const chatData = await chatResponse.json();\n            const chatHistory = Array.isArray(chatData.data) ? chatData.data : [\n                chatData.data\n            ];\n            // Fetch all subjects to get subject and topic details\n            const allSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            // Analyze chat history to generate intelligent recommendations\n            const subjectAnalysis = analyzeUserLearningPatterns(chatHistory, allSubjects);\n            // Generate recommendations based on analysis\n            const intelligentRecommendations = generateQuizRecommendations(subjectAnalysis);\n            setRecommendations(intelligentRecommendations);\n        } catch (error) {\n            console.error('Error loading recommendations:', error);\n            // Fallback to some default recommendations\n            setRecommendations([\n                {\n                    subject: \"Mathematics\",\n                    score: \"Practice Needed\",\n                    color: \"bg-blue-50\",\n                    buttonColor: \"bg-blue-500\"\n                },\n                {\n                    subject: \"Physics\",\n                    score: \"Review Required\",\n                    color: \"bg-orange-50\",\n                    buttonColor: \"bg-orange-500\"\n                },\n                {\n                    subject: \"Chemistry\",\n                    score: \"Quick Quiz\",\n                    color: \"bg-green-50\",\n                    buttonColor: \"bg-green-500\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__.AppSidebar, {\n                currentPage: \"dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-medium text-gray-800 mb-4\",\n                                    children: \"Welcome back, Student \\uD83D\\uDC4B\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"Continue Learning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: subjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__.SubjectCard, {\n                                                subject: subject.subject,\n                                                color: subject.color,\n                                                image: subject.image,\n                                                subjectId: subject.id\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Smart Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__.RecommendationCard, {\n                                                subject: rec.subject,\n                                                score: rec.score,\n                                                color: rec.color,\n                                                buttonColor: rec.buttonColor\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"OJt/C7Hd+E1pZMf9rSalgfCehPo=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ3NCO0FBQ2M7QUFDTjtBQUNWO0FBQ1E7QUFDckM7QUFDa0I7QUFDRztBQTBDdkQsTUFBTVcsZ0JBQWdCO0lBQUM7SUFBZTtJQUFpQjtJQUFjO0lBQWdCO0lBQWlCO0NBQWdCO0FBRXZHLFNBQVNDOztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2QsK0NBQVFBLENBQWlCLEVBQUU7SUFDM0QsTUFBTSxDQUFDZSxpQkFBaUJDLG1CQUFtQixHQUFHaEIsK0NBQVFBLENBQXVCLEVBQUU7SUFDL0UsTUFBTSxDQUFDaUIsU0FBU0MsV0FBVyxHQUFHbEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxFQUFFbUIsY0FBYyxFQUFFLEdBQUdULHFFQUFPQTtJQUVsQ1QsZ0RBQVNBOytCQUFDO1lBQ1JtQjtZQUNBQztRQUNGOzhCQUFHLEVBQUU7SUFFTCxNQUFNRCxlQUFlO1FBQ25CLElBQUk7WUFDRkYsV0FBVztZQUNYLE1BQU1JLGNBQWMsTUFBTWIscURBQVVBLENBQUNjLE1BQU07WUFFM0MsTUFBTUMsZ0JBQWdDRixZQUFZRyxHQUFHLENBQUMsQ0FBQ0MsU0FBU0MsUUFBVztvQkFDekVDLElBQUlGLFFBQVFHLEdBQUc7b0JBQ2ZILFNBQVNBLFFBQVFJLElBQUk7b0JBQ3JCQyxPQUFPcEIsYUFBYSxDQUFDZ0IsUUFBUWhCLGNBQWNxQixNQUFNLENBQUM7b0JBQ2xEQyxPQUFPO2dCQUNUO1lBRUFuQixZQUFZVTtRQUNkLEVBQUUsT0FBT1UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQywrQkFBK0I7WUFDL0JwQixZQUFZO2dCQUNWO29CQUFFYyxJQUFJO29CQUFLRixTQUFTO29CQUFTSyxPQUFPO29CQUFlRSxPQUFPO2dCQUFzQztnQkFDaEc7b0JBQUVMLElBQUk7b0JBQUtGLFNBQVM7b0JBQVdLLE9BQU87b0JBQWlCRSxPQUFPO2dCQUFzQztnQkFDcEc7b0JBQUVMLElBQUk7b0JBQUtGLFNBQVM7b0JBQVdLLE9BQU87b0JBQWNFLE9BQU87Z0JBQXNDO2dCQUNqRztvQkFBRUwsSUFBSTtvQkFBS0YsU0FBUztvQkFBYUssT0FBTztvQkFBZ0JFLE9BQU87Z0JBQXNDO2FBQ3RHO1FBQ0gsU0FBVTtZQUNSZixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1HLHNCQUFzQjtRQUMxQixJQUFJO1lBQ0ZILFdBQVc7WUFFWCx5REFBeUQ7WUFDekQsTUFBTWtCLGVBQWUsTUFBTUMsTUFDekIsR0FBbUMsT0FBaENDLHVCQUErQixFQUFDLHVCQUNuQztnQkFDRUcsU0FBU3RCO1lBQ1g7WUFHRixJQUFJLENBQUNpQixhQUFhTSxFQUFFLEVBQUU7Z0JBQ3BCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU1DLFdBQVcsTUFBTVIsYUFBYVMsSUFBSTtZQUN4QyxNQUFNQyxjQUFpQ0MsTUFBTUMsT0FBTyxDQUFDSixTQUFTSyxJQUFJLElBQUlMLFNBQVNLLElBQUksR0FBRztnQkFBQ0wsU0FBU0ssSUFBSTthQUFDO1lBRXJHLHNEQUFzRDtZQUN0RCxNQUFNQyxjQUFjLE1BQU16QyxxREFBVUEsQ0FBQ2MsTUFBTTtZQUUzQywrREFBK0Q7WUFDL0QsTUFBTTRCLGtCQUFrQkMsNEJBQTRCTixhQUFhSTtZQUVqRSw2Q0FBNkM7WUFDN0MsTUFBTUcsNkJBQTZCQyw0QkFBNEJIO1lBRS9EbkMsbUJBQW1CcUM7UUFDckIsRUFBRSxPQUFPbkIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRCwyQ0FBMkM7WUFDM0NsQixtQkFBbUI7Z0JBQ2pCO29CQUFFVSxTQUFTO29CQUFlNkIsT0FBTztvQkFBbUJ4QixPQUFPO29CQUFjeUIsYUFBYTtnQkFBYztnQkFDcEc7b0JBQUU5QixTQUFTO29CQUFXNkIsT0FBTztvQkFBbUJ4QixPQUFPO29CQUFnQnlCLGFBQWE7Z0JBQWdCO2dCQUNwRztvQkFBRTlCLFNBQVM7b0JBQWE2QixPQUFPO29CQUFjeEIsT0FBTztvQkFBZXlCLGFBQWE7Z0JBQWU7YUFDaEc7UUFDSCxTQUFVO1lBQ1J0QyxXQUFXO1FBQ2I7SUFDRjtJQUNBLHFCQUNFLDhEQUFDWixtRUFBZUE7OzBCQUNkLDhEQUFDRCx5RUFBVUE7Z0JBQUNvRCxhQUFZOzs7Ozs7MEJBQ3hCLDhEQUFDbEQsZ0VBQVlBOztrQ0FDWCw4REFBQ0gsbUZBQWVBOzs7OztrQ0FFaEIsOERBQUNzRDt3QkFBS0MsV0FBVTs7MENBRWQsOERBQUNDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDRTtvQ0FBR0YsV0FBVTs4Q0FBeUM7Ozs7Ozs7Ozs7OzBDQUl6RCw4REFBQ0c7O2tEQUNDLDhEQUFDQzt3Q0FBR0osV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNDO3dDQUFJRCxXQUFVO2tEQUNaOUMsU0FBU1ksR0FBRyxDQUFDLENBQUNDLFNBQVNDLHNCQUN0Qiw4REFBQ3pCLDJFQUFXQTtnREFFVndCLFNBQVNBLFFBQVFBLE9BQU87Z0RBQ3hCSyxPQUFPTCxRQUFRSyxLQUFLO2dEQUNwQkUsT0FBT1AsUUFBUU8sS0FBSztnREFDcEIrQixXQUFXdEMsUUFBUUUsRUFBRTsrQ0FKaEJEOzs7Ozs7Ozs7Ozs7Ozs7OzBDQVdiLDhEQUFDbUM7O2tEQUNDLDhEQUFDRjt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNuRCwrRUFBR0E7Z0RBQUNtRCxXQUFVOzs7Ozs7MERBQ2YsOERBQUNJO2dEQUFHSixXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQ1o1QyxnQkFBZ0JVLEdBQUcsQ0FBQyxDQUFDd0MsS0FBS3RDLHNCQUN6Qiw4REFBQ3hCLHlGQUFrQkE7Z0RBRWpCdUIsU0FBU3VDLElBQUl2QyxPQUFPO2dEQUNwQjZCLE9BQU9VLElBQUlWLEtBQUs7Z0RBQ2hCeEIsT0FBT2tDLElBQUlsQyxLQUFLO2dEQUNoQnlCLGFBQWFTLElBQUlULFdBQVc7K0NBSnZCN0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFhdkI7R0FqSXdCZjs7UUFJS0YsaUVBQU9BOzs7S0FKWkUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWRhcnNcXERlc2t0b3BcXEZMXFxWZWxvY2l0eVxcc3R1ZHlidWRkeS1mcm9udGVuZFxcc3JjXFxhcHBcXGRhc2hib2FyZFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBTdWJqZWN0Q2FyZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3N1YmplY3QtY2FyZFwiXG5pbXBvcnQgeyBSZWNvbW1lbmRhdGlvbkNhcmQgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9yZWNvbW1lbmRhdGlvbi1jYXJkXCJcbmltcG9ydCB7IERhc2hib2FyZEhlYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2Rhc2hib2FyZC1oZWFkZXJcIlxuaW1wb3J0IHsgQXBwU2lkZWJhciB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2FwcC1zaWRlYmFyXCJcbmltcG9ydCB7IFNpZGViYXJQcm92aWRlciwgU2lkZWJhckluc2V0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zaWRlYmFyXCJcbmltcG9ydCB7IFphcCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgc3ViamVjdEFwaSwgU3ViamVjdCB9IGZyb20gXCJAL2xpYi9hcGkvcXVpelwiXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvaG9va3MvdXNlQXV0aGVudGljYXRpb25Ib29rXCJcblxuaW50ZXJmYWNlIExvY2FsU3ViamVjdCB7XG4gIHN1YmplY3Q6IHN0cmluZ1xuICBjb2xvcjogc3RyaW5nXG4gIGltYWdlOiBzdHJpbmdcbiAgaWQ6IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgUXVpelJlY29tbWVuZGF0aW9uIHtcbiAgc3ViamVjdDogc3RyaW5nXG4gIHNjb3JlOiBzdHJpbmdcbiAgY29sb3I6IHN0cmluZ1xuICBidXR0b25Db2xvcjogc3RyaW5nXG4gIHN1YmplY3RJZD86IHN0cmluZ1xuICB0b3BpY0lkPzogc3RyaW5nXG4gIHRvcGljTmFtZT86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgQ2hhdEhpc3RvcnlJdGVtIHtcbiAgX2lkOiBzdHJpbmdcbiAgZGF0ZTogc3RyaW5nXG4gIHN1YmplY3RXaXNlOiBBcnJheTx7XG4gICAgc3ViamVjdDogc3RyaW5nXG4gICAgcXVlcmllczogQXJyYXk8e1xuICAgICAgcXVlcnk6IHN0cmluZ1xuICAgICAgcmVzcG9uc2U6IHN0cmluZ1xuICAgICAgdG9rZW5zVXNlZDogbnVtYmVyXG4gICAgICBzdW1tYXJ5Pzogc3RyaW5nXG4gICAgICBfaWQ6IHN0cmluZ1xuICAgICAgY3JlYXRlZEF0OiBzdHJpbmdcbiAgICAgIHVwZGF0ZWRBdDogc3RyaW5nXG4gICAgfT5cbiAgICBfaWQ6IHN0cmluZ1xuICB9PlxuICB0b3RhbFRva2Vuc1NwZW50OiBudW1iZXJcbiAgc3ViamVjdHM6IHN0cmluZ1tdXG4gIHVzZXJJZDogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG59XG5cbmNvbnN0IHN1YmplY3RDb2xvcnMgPSBbXCJiZy1ibHVlLTUwMFwiLCBcImJnLW9yYW5nZS01MDBcIiwgXCJiZy1yZWQtNTAwXCIsIFwiYmctZ3JlZW4tNTAwXCIsIFwiYmctcHVycGxlLTUwMFwiLCBcImJnLXllbGxvdy01MDBcIl1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkKCkge1xuICBjb25zdCBbc3ViamVjdHMsIHNldFN1YmplY3RzXSA9IHVzZVN0YXRlPExvY2FsU3ViamVjdFtdPihbXSlcbiAgY29uc3QgW3JlY29tbWVuZGF0aW9ucywgc2V0UmVjb21tZW5kYXRpb25zXSA9IHVzZVN0YXRlPFF1aXpSZWNvbW1lbmRhdGlvbltdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHsgZ2V0QXV0aEhlYWRlcnMgfSA9IHVzZUF1dGgoKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFN1YmplY3RzKClcbiAgICBsb2FkUmVjb21tZW5kYXRpb25zKClcbiAgfSwgW10pXG5cbiAgY29uc3QgbG9hZFN1YmplY3RzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBjb25zdCBhcGlTdWJqZWN0cyA9IGF3YWl0IHN1YmplY3RBcGkuZ2V0QWxsKClcblxuICAgICAgY29uc3QgbG9jYWxTdWJqZWN0czogTG9jYWxTdWJqZWN0W10gPSBhcGlTdWJqZWN0cy5tYXAoKHN1YmplY3QsIGluZGV4KSA9PiAoe1xuICAgICAgICBpZDogc3ViamVjdC5faWQsXG4gICAgICAgIHN1YmplY3Q6IHN1YmplY3QubmFtZSxcbiAgICAgICAgY29sb3I6IHN1YmplY3RDb2xvcnNbaW5kZXggJSBzdWJqZWN0Q29sb3JzLmxlbmd0aF0sXG4gICAgICAgIGltYWdlOiBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTY0JndpZHRoPTY0XCJcbiAgICAgIH0pKVxuXG4gICAgICBzZXRTdWJqZWN0cyhsb2NhbFN1YmplY3RzKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBzdWJqZWN0czonLCBlcnJvcilcbiAgICAgIC8vIEZhbGxiYWNrIHRvIGRlZmF1bHQgc3ViamVjdHNcbiAgICAgIHNldFN1YmplY3RzKFtcbiAgICAgICAgeyBpZDogXCIxXCIsIHN1YmplY3Q6IFwiTWF0aHNcIiwgY29sb3I6IFwiYmctYmx1ZS01MDBcIiwgaW1hZ2U6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9NjQmd2lkdGg9NjRcIiB9LFxuICAgICAgICB7IGlkOiBcIjJcIiwgc3ViamVjdDogXCJQaHlzaWNzXCIsIGNvbG9yOiBcImJnLW9yYW5nZS01MDBcIiwgaW1hZ2U6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9NjQmd2lkdGg9NjRcIiB9LFxuICAgICAgICB7IGlkOiBcIjNcIiwgc3ViamVjdDogXCJCaW9sb2d5XCIsIGNvbG9yOiBcImJnLXJlZC01MDBcIiwgaW1hZ2U6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9NjQmd2lkdGg9NjRcIiB9LFxuICAgICAgICB7IGlkOiBcIjRcIiwgc3ViamVjdDogXCJDaGVtaXN0cnlcIiwgY29sb3I6IFwiYmctZ3JlZW4tNTAwXCIsIGltYWdlOiBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTY0JndpZHRoPTY0XCIgfSxcbiAgICAgIF0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbG9hZFJlY29tbWVuZGF0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKVxuXG4gICAgICAvLyBGZXRjaCBjaGF0IGhpc3RvcnkgdG8gYW5hbHl6ZSB1c2VyJ3MgbGVhcm5pbmcgcGF0dGVybnNcbiAgICAgIGNvbnN0IGNoYXRSZXNwb25zZSA9IGF3YWl0IGZldGNoKFxuICAgICAgICBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9jaGF0L2NoYXQtaGlzdG9yeWAsXG4gICAgICAgIHtcbiAgICAgICAgICBoZWFkZXJzOiBnZXRBdXRoSGVhZGVycygpLFxuICAgICAgICB9XG4gICAgICApXG5cbiAgICAgIGlmICghY2hhdFJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGNoYXQgaGlzdG9yeScpXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGNoYXREYXRhID0gYXdhaXQgY2hhdFJlc3BvbnNlLmpzb24oKVxuICAgICAgY29uc3QgY2hhdEhpc3Rvcnk6IENoYXRIaXN0b3J5SXRlbVtdID0gQXJyYXkuaXNBcnJheShjaGF0RGF0YS5kYXRhKSA/IGNoYXREYXRhLmRhdGEgOiBbY2hhdERhdGEuZGF0YV1cblxuICAgICAgLy8gRmV0Y2ggYWxsIHN1YmplY3RzIHRvIGdldCBzdWJqZWN0IGFuZCB0b3BpYyBkZXRhaWxzXG4gICAgICBjb25zdCBhbGxTdWJqZWN0cyA9IGF3YWl0IHN1YmplY3RBcGkuZ2V0QWxsKClcblxuICAgICAgLy8gQW5hbHl6ZSBjaGF0IGhpc3RvcnkgdG8gZ2VuZXJhdGUgaW50ZWxsaWdlbnQgcmVjb21tZW5kYXRpb25zXG4gICAgICBjb25zdCBzdWJqZWN0QW5hbHlzaXMgPSBhbmFseXplVXNlckxlYXJuaW5nUGF0dGVybnMoY2hhdEhpc3RvcnksIGFsbFN1YmplY3RzKVxuXG4gICAgICAvLyBHZW5lcmF0ZSByZWNvbW1lbmRhdGlvbnMgYmFzZWQgb24gYW5hbHlzaXNcbiAgICAgIGNvbnN0IGludGVsbGlnZW50UmVjb21tZW5kYXRpb25zID0gZ2VuZXJhdGVRdWl6UmVjb21tZW5kYXRpb25zKHN1YmplY3RBbmFseXNpcylcblxuICAgICAgc2V0UmVjb21tZW5kYXRpb25zKGludGVsbGlnZW50UmVjb21tZW5kYXRpb25zKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHJlY29tbWVuZGF0aW9uczonLCBlcnJvcilcbiAgICAgIC8vIEZhbGxiYWNrIHRvIHNvbWUgZGVmYXVsdCByZWNvbW1lbmRhdGlvbnNcbiAgICAgIHNldFJlY29tbWVuZGF0aW9ucyhbXG4gICAgICAgIHsgc3ViamVjdDogXCJNYXRoZW1hdGljc1wiLCBzY29yZTogXCJQcmFjdGljZSBOZWVkZWRcIiwgY29sb3I6IFwiYmctYmx1ZS01MFwiLCBidXR0b25Db2xvcjogXCJiZy1ibHVlLTUwMFwiIH0sXG4gICAgICAgIHsgc3ViamVjdDogXCJQaHlzaWNzXCIsIHNjb3JlOiBcIlJldmlldyBSZXF1aXJlZFwiLCBjb2xvcjogXCJiZy1vcmFuZ2UtNTBcIiwgYnV0dG9uQ29sb3I6IFwiYmctb3JhbmdlLTUwMFwiIH0sXG4gICAgICAgIHsgc3ViamVjdDogXCJDaGVtaXN0cnlcIiwgc2NvcmU6IFwiUXVpY2sgUXVpelwiLCBjb2xvcjogXCJiZy1ncmVlbi01MFwiLCBidXR0b25Db2xvcjogXCJiZy1ncmVlbi01MDBcIiB9LFxuICAgICAgXSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cbiAgcmV0dXJuIChcbiAgICA8U2lkZWJhclByb3ZpZGVyPlxuICAgICAgPEFwcFNpZGViYXIgY3VycmVudFBhZ2U9XCJkYXNoYm9hcmRcIiAvPlxuICAgICAgPFNpZGViYXJJbnNldD5cbiAgICAgICAgPERhc2hib2FyZEhlYWRlciAvPlxuXG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBwLTQgbWQ6cC02IHNwYWNlLXktNiBiZy1ncmF5LTUwIG1pbi1oLXNjcmVlblwiPlxuICAgICAgICAgIHsvKiBNb2JpbGUgV2VsY29tZSBNZXNzYWdlICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6aGlkZGVuXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIG1iLTRcIj5XZWxjb21lIGJhY2ssIFN0dWRlbnQg8J+RizwvaDE+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQ29udGludWUgTGVhcm5pbmcgU2VjdGlvbiAqL31cbiAgICAgICAgICA8c2VjdGlvbj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi00XCI+Q29udGludWUgTGVhcm5pbmc8L2gyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgICAgIHtzdWJqZWN0cy5tYXAoKHN1YmplY3QsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPFN1YmplY3RDYXJkXG4gICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgc3ViamVjdD17c3ViamVjdC5zdWJqZWN0fVxuICAgICAgICAgICAgICAgICAgY29sb3I9e3N1YmplY3QuY29sb3J9XG4gICAgICAgICAgICAgICAgICBpbWFnZT17c3ViamVjdC5pbWFnZX1cbiAgICAgICAgICAgICAgICAgIHN1YmplY3RJZD17c3ViamVjdC5pZH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgIHsvKiBTbWFydCBSZWNvbW1lbmRhdGlvbnMgU2VjdGlvbiAqL31cbiAgICAgICAgICA8c2VjdGlvbj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItNFwiPlxuICAgICAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTUwMFwiIC8+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPlNtYXJ0IFJlY29tbWVuZGF0aW9uczwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgICB7cmVjb21tZW5kYXRpb25zLm1hcCgocmVjLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxSZWNvbW1lbmRhdGlvbkNhcmRcbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICBzdWJqZWN0PXtyZWMuc3ViamVjdH1cbiAgICAgICAgICAgICAgICAgIHNjb3JlPXtyZWMuc2NvcmV9XG4gICAgICAgICAgICAgICAgICBjb2xvcj17cmVjLmNvbG9yfVxuICAgICAgICAgICAgICAgICAgYnV0dG9uQ29sb3I9e3JlYy5idXR0b25Db2xvcn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9TaWRlYmFySW5zZXQ+XG4gICAgPC9TaWRlYmFyUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlN1YmplY3RDYXJkIiwiUmVjb21tZW5kYXRpb25DYXJkIiwiRGFzaGJvYXJkSGVhZGVyIiwiQXBwU2lkZWJhciIsIlNpZGViYXJQcm92aWRlciIsIlNpZGViYXJJbnNldCIsIlphcCIsInN1YmplY3RBcGkiLCJ1c2VBdXRoIiwic3ViamVjdENvbG9ycyIsIkRhc2hib2FyZCIsInN1YmplY3RzIiwic2V0U3ViamVjdHMiLCJyZWNvbW1lbmRhdGlvbnMiLCJzZXRSZWNvbW1lbmRhdGlvbnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImdldEF1dGhIZWFkZXJzIiwibG9hZFN1YmplY3RzIiwibG9hZFJlY29tbWVuZGF0aW9ucyIsImFwaVN1YmplY3RzIiwiZ2V0QWxsIiwibG9jYWxTdWJqZWN0cyIsIm1hcCIsInN1YmplY3QiLCJpbmRleCIsImlkIiwiX2lkIiwibmFtZSIsImNvbG9yIiwibGVuZ3RoIiwiaW1hZ2UiLCJlcnJvciIsImNvbnNvbGUiLCJjaGF0UmVzcG9uc2UiLCJmZXRjaCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiaGVhZGVycyIsIm9rIiwiRXJyb3IiLCJjaGF0RGF0YSIsImpzb24iLCJjaGF0SGlzdG9yeSIsIkFycmF5IiwiaXNBcnJheSIsImRhdGEiLCJhbGxTdWJqZWN0cyIsInN1YmplY3RBbmFseXNpcyIsImFuYWx5emVVc2VyTGVhcm5pbmdQYXR0ZXJucyIsImludGVsbGlnZW50UmVjb21tZW5kYXRpb25zIiwiZ2VuZXJhdGVRdWl6UmVjb21tZW5kYXRpb25zIiwic2NvcmUiLCJidXR0b25Db2xvciIsImN1cnJlbnRQYWdlIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiIsImgxIiwic2VjdGlvbiIsImgyIiwic3ViamVjdElkIiwicmVjIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});