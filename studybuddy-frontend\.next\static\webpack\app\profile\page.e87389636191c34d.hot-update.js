"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/components/layout/activity-heatmap.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/activity-heatmap.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivityHeatmap: () => (/* binding */ ActivityHeatmap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _heatMapGrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heatMapGrid */ \"(app-pages-browser)/./src/components/layout/heatMapGrid.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ActivityHeatmap(param) {\n    let { userData } = param;\n    _s();\n    const [heatMapData, setHeatMapData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [period, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"monthly\");\n    function getDateRange() {\n        let period = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'monthly';\n        const endDate = new Date();\n        const startDate = new Date();\n        switch(period){\n            case 'daily':\n                startDate.setDate(startDate.getDate() - 1);\n                break;\n            case 'weekly':\n                startDate.setDate(startDate.getDate() - 7);\n                break;\n            case 'monthly':\n            default:\n                startDate.setMonth(startDate.getMonth() - 1);\n                break;\n        }\n        return [\n            startDate.toISOString().split('T')[0],\n            endDate.toISOString().split('T')[0]\n        ];\n    }\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        \"ActivityHeatmap.useState\": ()=>getDateRange()\n    }[\"ActivityHeatmap.useState\"]);\n    const handlePeriodChange = (value)=>{\n        const range = getDateRange(value);\n        setDateRange(range);\n        setPeriods(value);\n    };\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const fetchHeatMapData = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat/heat-map?upperBound=\").concat(dateRange[1], \"&lowerBound=\").concat(dateRange[0]), {\n                method: 'GET',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch user data');\n            }\n            const data = await response.json();\n            setHeatMapData(data);\n        } catch (error) {\n            console.error('Error fetching user data:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"ActivityHeatmap.useEffect\": ()=>{\n            fetchHeatMapData();\n        }\n    }[\"ActivityHeatmap.useEffect\"], [\n        fetchHeatMapData\n    ]);\n    const subjectColors = {\n        English: 'bg-red-500',\n        Mathematics: 'bg-blue-500',\n        Geometry: 'bg-green-600',\n        Algebra: 'bg-purple-500',\n        Numerical: 'bg-indigo-400',\n        Science: 'bg-teal-500',\n        Chemistry: 'bg-cyan-400',\n        Biology: 'bg-emerald-500',\n        Physics: 'bg-orange-400',\n        'Social Science': 'bg-pink-500',\n        Geography: 'bg-yellow-500',\n        Economics: 'bg-lime-500',\n        'Political Science': 'bg-rose-500',\n        History: 'bg-amber-500',\n        'Computer Science': 'bg-sky-500',\n        Electronics: 'bg-fuchsia-500',\n        Electricals: 'bg-violet-500',\n        Statistics: 'bg-gray-500',\n        default: 'bg-slate-500'\n    };\n    const getSubjectColor = (subject)=>{\n        return subjectColors[subject] || subjectColors.default;\n    };\n    const getUniqueSubjects = (data)=>{\n        const subjectSet = new Set();\n        data.forEach((entry)=>{\n            entry.subjects.forEach((subject)=>{\n                subjectSet.add(subject);\n            });\n        });\n        return Array.from(subjectSet);\n    };\n    const uniqueSubjects = getUniqueSubjects(heatMapData);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 rounded-[22.5px] border-2 border-blue-200 bg-white h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-[50%] overflow-hidden bg-blue-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/assets/buddy/Joy-profile-icon.svg\",\n                                    alt: \"Recommendation mascot\",\n                                    width: 48,\n                                    height: 48,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-800\",\n                                        children: \"Here's what you have\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-800\",\n                                        children: [\n                                            \"been sweating on \",\n                                            userData.name,\n                                            \",\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.Select, {\n                            onValueChange: handlePeriodChange,\n                            defaultValue: \"monthly\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectValue, {\n                                        placeholder: \"monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectItem, {\n                                            value: \"monthly\",\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectItem, {\n                                            value: \"weekly\",\n                                            children: \"Weekly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectItem, {\n                                            value: \"daily\",\n                                            children: \"Daily\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 rounded-[14px] border border-blue-200 bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-700\",\n                                    children: \"Subjects Explored\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm font-medium text-gray-800\",\n                                    children: uniqueSubjects === null || uniqueSubjects === void 0 ? void 0 : uniqueSubjects.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: uniqueSubjects === null || uniqueSubjects === void 0 ? void 0 : uniqueSubjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(getSubjectColor(subject))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: subject\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heatMapGrid__WEBPACK_IMPORTED_MODULE_5__.HeatmapGrid, {\n                                heatMapData: heatMapData,\n                                subjectColors: subjectColors,\n                                period: period\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(ActivityHeatmap, \"Ypi8JXmBvaSTLqe6tfqZiFqd7qA=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ActivityHeatmap;\nvar _c;\n$RefreshReg$(_c, \"ActivityHeatmap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/activity-heatmap.tsx\n"));

/***/ })

});