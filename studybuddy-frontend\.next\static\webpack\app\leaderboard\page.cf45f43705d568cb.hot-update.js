"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leaderboard/page",{

/***/ "(app-pages-browser)/./src/app/leaderboard/page.tsx":
/*!**************************************!*\
  !*** ./src/app/leaderboard/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LeaderboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_dashboard_podium__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/podium */ \"(app-pages-browser)/./src/components/dashboard/podium.tsx\");\n/* harmony import */ var _components_dashboard_leaderboard_list__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/leaderboard-list */ \"(app-pages-browser)/./src/components/dashboard/leaderboard-list.tsx\");\n/* harmony import */ var _components_dashboard_profile_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/profile-card */ \"(app-pages-browser)/./src/components/dashboard/profile-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(app-pages-browser)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Sample data\nconst leaderboardUsers = [\n    {\n        id: \"1\",\n        name: \"Arjun Krishna\",\n        score: 143,\n        avatar: \"/placeholder.svg?height=60&width=60\",\n        studentId: \"234015\",\n        position: 1\n    },\n    {\n        id: \"2\",\n        name: \"Kamal\",\n        score: 120,\n        avatar: \"/placeholder.svg?height=60&width=60\",\n        studentId: \"234016\",\n        position: 2\n    },\n    {\n        id: \"3\",\n        name: \"Akash\",\n        score: 119,\n        avatar: \"/placeholder.svg?height=60&width=60\",\n        studentId: \"234017\",\n        position: 3\n    }\n];\nconst currentUser = {\n    name: \"Arjun Krishna\",\n    score: 143,\n    avatar: \"/placeholder.svg?height=80&width=80\",\n    position: 1\n};\nfunction LeaderboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__.AppSidebar, {\n                currentPage: \"dashboard/leaderboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Leaderboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 border border-orange-200 rounded-lg p-4 flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-orange-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Earn sparks (\\uD83D\\uDD25)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" and climb the leaderboards by showing up every day, staying consistent, and getting your answers right.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_podium__WEBPACK_IMPORTED_MODULE_1__.Podium, {\n                                users: leaderboardUsers\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_leaderboard_list__WEBPACK_IMPORTED_MODULE_2__.LeaderboardList, {\n                                            users: leaderboardUsers\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_profile_card__WEBPACK_IMPORTED_MODULE_3__.ProfileCard, {\n                                            user: currentUser\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_profile_card__WEBPACK_IMPORTED_MODULE_3__.ProfileCard, {\n                                    user: currentUser\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\leaderboard\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/leaderboard/page.tsx\n"));

/***/ })

});