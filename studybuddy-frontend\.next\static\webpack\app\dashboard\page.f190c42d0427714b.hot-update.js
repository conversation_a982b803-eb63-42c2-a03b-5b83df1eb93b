"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/subject-card */ \"(app-pages-browser)/./src/components/dashboard/subject-card.tsx\");\n/* harmony import */ var _components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/recommendation-card */ \"(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(app-pages-browser)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst subjectColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-red-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-yellow-500\"\n];\n// Helper function to analyze user's learning patterns from chat history\nconst analyzeUserLearningPatterns = (chatHistory, subjects)=>{\n    const subjectMap = new Map();\n    subjects.forEach((subject)=>{\n        subjectMap.set(subject._id, subject);\n    });\n    const analysis = new Map();\n    chatHistory.forEach((historyItem)=>{\n        // Check if this is recent activity (within last 7 days)\n        const isRecent = new Date(historyItem.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);\n        historyItem.subjectWise.forEach((subjectData)=>{\n            const subject = subjectMap.get(subjectData.subject);\n            if (subject) {\n                const existing = analysis.get(subject._id) || {\n                    subject,\n                    queryCount: 0,\n                    topics: new Set(),\n                    recentActivity: false\n                };\n                existing.queryCount += subjectData.queries.length;\n                existing.recentActivity = existing.recentActivity || isRecent;\n                // Extract topics from queries (simplified - could be enhanced with NLP)\n                subjectData.queries.forEach((query)=>{\n                    subject.topics.forEach((topic)=>{\n                        if (query.query.toLowerCase().includes(topic.name.toLowerCase()) || query.response.toLowerCase().includes(topic.name.toLowerCase())) {\n                            existing.topics.add(topic.name);\n                        }\n                    });\n                });\n                analysis.set(subject._id, existing);\n            }\n        });\n    });\n    return analysis;\n};\n// Helper function to generate quiz recommendations based on analysis\nconst generateQuizRecommendations = (analysis)=>{\n    const recommendations = [];\n    const colors = [\n        {\n            bg: \"bg-blue-50\",\n            button: \"bg-blue-500\"\n        },\n        {\n            bg: \"bg-green-50\",\n            button: \"bg-green-500\"\n        },\n        {\n            bg: \"bg-orange-50\",\n            button: \"bg-orange-500\"\n        },\n        {\n            bg: \"bg-purple-50\",\n            button: \"bg-purple-500\"\n        },\n        {\n            bg: \"bg-red-50\",\n            button: \"bg-red-500\"\n        },\n        {\n            bg: \"bg-indigo-50\",\n            button: \"bg-indigo-500\"\n        }\n    ];\n    // Sort subjects by query count (most asked questions first)\n    const sortedSubjects = Array.from(analysis.entries()).sort((param, param1)=>{\n        let [, a] = param, [, b] = param1;\n        return b.queryCount - a.queryCount;\n    }).slice(0, 6) // Limit to top 6 subjects\n    ;\n    sortedSubjects.forEach((param, index)=>{\n        let [_, data] = param;\n        const colorIndex = index % colors.length;\n        const { subject, queryCount, topics, recentActivity } = data;\n        // Generate recommendation score based on activity\n        let score = \"Quick Review\";\n        if (queryCount > 10) {\n            score = \"Practice Needed\";\n        } else if (queryCount > 5) {\n            score = \"Review Required\";\n        } else if (recentActivity) {\n            score = \"Fresh Topic\";\n        }\n        // If we have specific topics, recommend quiz for the most discussed topic\n        const topicArray = Array.from(topics);\n        const recommendedTopic = topicArray.length > 0 ? topicArray[0] : undefined;\n        const topicObj = recommendedTopic ? subject.topics.find((t)=>t.name === recommendedTopic) : undefined;\n        recommendations.push({\n            subject: subject.name,\n            score,\n            color: colors[colorIndex].bg,\n            buttonColor: colors[colorIndex].button,\n            subjectId: subject._id,\n            topicId: topicObj === null || topicObj === void 0 ? void 0 : topicObj._id,\n            topicName: topicObj === null || topicObj === void 0 ? void 0 : topicObj.name\n        });\n    });\n    // If no chat history, provide some default recommendations\n    if (recommendations.length === 0) {\n        return [\n            {\n                subject: \"Mathematics\",\n                score: \"Get Started\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Physics\",\n                score: \"Explore\",\n                color: \"bg-green-50\",\n                buttonColor: \"bg-green-500\"\n            },\n            {\n                subject: \"Chemistry\",\n                score: \"Try Now\",\n                color: \"bg-orange-50\",\n                buttonColor: \"bg-orange-500\"\n            }\n        ];\n    }\n    return recommendations;\n};\nfunction Dashboard() {\n    _s();\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            loadSubjects();\n            loadRecommendations();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const loadSubjects = async ()=>{\n        try {\n            setLoading(true);\n            const apiSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            const localSubjects = apiSubjects.map((subject, index)=>({\n                    id: subject._id,\n                    subject: subject.name,\n                    color: subjectColors[index % subjectColors.length],\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }));\n            setSubjects(localSubjects);\n        } catch (error) {\n            console.error('Failed to load subjects:', error);\n            // Fallback to default subjects\n            setSubjects([\n                {\n                    id: \"1\",\n                    subject: \"Maths\",\n                    color: \"bg-blue-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"2\",\n                    subject: \"Physics\",\n                    color: \"bg-orange-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"3\",\n                    subject: \"Biology\",\n                    color: \"bg-red-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"4\",\n                    subject: \"Chemistry\",\n                    color: \"bg-green-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRecommendations = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch chat history to analyze user's learning patterns\n            const chatResponse = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat/chat-history\"), {\n                headers: getAuthHeaders()\n            });\n            if (!chatResponse.ok) {\n                throw new Error('Failed to fetch chat history');\n            }\n            const chatData = await chatResponse.json();\n            const chatHistory = Array.isArray(chatData.data) ? chatData.data : [\n                chatData.data\n            ];\n            // Fetch all subjects to get subject and topic details\n            const allSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            // Analyze chat history to generate intelligent recommendations\n            const subjectAnalysis = analyzeUserLearningPatterns(chatHistory, allSubjects);\n            // Generate recommendations based on analysis\n            const intelligentRecommendations = generateQuizRecommendations(subjectAnalysis);\n            setRecommendations(intelligentRecommendations);\n        } catch (error) {\n            console.error('Error loading recommendations:', error);\n            // Fallback to some default recommendations\n            setRecommendations([\n                {\n                    subject: \"Mathematics\",\n                    score: \"Practice Needed\",\n                    color: \"bg-blue-50\",\n                    buttonColor: \"bg-blue-500\"\n                },\n                {\n                    subject: \"Physics\",\n                    score: \"Review Required\",\n                    color: \"bg-orange-50\",\n                    buttonColor: \"bg-orange-500\"\n                },\n                {\n                    subject: \"Chemistry\",\n                    score: \"Quick Quiz\",\n                    color: \"bg-green-50\",\n                    buttonColor: \"bg-green-500\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__.AppSidebar, {\n                currentPage: \"dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-medium text-gray-800 mb-4\",\n                                    children: \"Welcome back, Student \\uD83D\\uDC4B\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"Continue Learning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: subjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__.SubjectCard, {\n                                                subject: subject.subject,\n                                                color: subject.color,\n                                                image: subject.image,\n                                                subjectId: subject.id\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Smart Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__.RecommendationCard, {\n                                                subject: rec.subject,\n                                                score: rec.score,\n                                                color: rec.color,\n                                                buttonColor: rec.buttonColor,\n                                                subjectId: rec.subjectId,\n                                                topicId: rec.topicId,\n                                                topicName: rec.topicName\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"OJt/C7Hd+E1pZMf9rSalgfCehPo=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});