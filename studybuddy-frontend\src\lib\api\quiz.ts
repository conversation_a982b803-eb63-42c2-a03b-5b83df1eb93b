const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export interface Subject {
  _id: string;
  name: string;
  description?: string;
  topics: Topic[];
}

export interface Topic {
  _id: string;
  name: string;
  description?: string;
}

export interface QuizOption {
  text: string;
  isCorrect: boolean;
}

export interface Quiz {
  _id: string;
  question: string;
  options: QuizOption[];
  subjectId: string;
  topicId: string;
  type?: string;
  difficulty?: number;
  explanation?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateQuizData {
  question: string;
  options: QuizOption[];
  subjectId: string;
  topicId: string;
  type?: string;
  difficulty?: number;
  explanation?: string;
}

export interface UpdateQuizData {
  question?: string;
  options?: QuizOption[];
  type?: string;
  difficulty?: number;
  explanation?: string;
}

export interface QuizFilter {
  subjectId?: string;
  topicId?: string;
  noOfQuestions?: number;
}

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('accessToken');
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};

// Subject API functions
export const subjectApi = {
  getAll: async (): Promise<Subject[]> => {
    const response = await fetch(`${API_BASE_URL}/users/subjects`, {
      headers: getAuthHeaders()
    });
    if (!response.ok) {
      throw new Error('Failed to fetch subjects');
    }
    return response.json();
  },

  getById: async (id: string): Promise<Subject> => {
    const response = await fetch(`${API_BASE_URL}/users/subjects/${id}`, {
      headers: getAuthHeaders()
    });
    if (!response.ok) {
      throw new Error('Failed to fetch subject');
    }
    return response.json();
  }
};

// Quiz API functions
export const quizApi = {
  getAll: async (filter?: QuizFilter): Promise<Quiz[]> => {
    const params = new URLSearchParams();
    if (filter?.subjectId) params.append('subjectId', filter.subjectId);
    if (filter?.topicId) params.append('topicId', filter.topicId);
    if (filter?.noOfQuestions) params.append('noOfQuestions', filter.noOfQuestions.toString());

    // Try user-facing endpoint first, fallback to admin endpoint
    let url = `${API_BASE_URL}/users/quizzes${params.toString() ? `?${params.toString()}` : ''}`;
    let response = await fetch(url, {
      headers: getAuthHeaders()
    });

    // If user endpoint doesn't exist, try admin endpoint
    if (!response.ok && response.status === 404) {
      url = `${API_BASE_URL}/admin/quizzes${params.toString() ? `?${params.toString()}` : ''}`;
      response = await fetch(url, {
        headers: getAuthHeaders()
      });
    }

    if (!response.ok) {
      throw new Error('Failed to fetch quizzes');
    }
    return response.json();
  },

  getById: async (id: string): Promise<Quiz> => {
    const response = await fetch(`${API_BASE_URL}/admin/quizzes/${id}`, {
      headers: getAuthHeaders()
    });
    if (!response.ok) {
      throw new Error('Failed to fetch quiz');
    }
    return response.json();
  },

  create: async (data: CreateQuizData): Promise<Quiz> => {
    const response = await fetch(`${API_BASE_URL}/admin/quizzes`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    if (!response.ok) {
      throw new Error('Failed to create quiz');
    }
    return response.json();
  },

  update: async (id: string, data: UpdateQuizData): Promise<Quiz> => {
    const response = await fetch(`${API_BASE_URL}/admin/quizzes/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data)
    });
    if (!response.ok) {
      throw new Error('Failed to update quiz');
    }
    return response.json();
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`${API_BASE_URL}/admin/quizzes/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });
    if (!response.ok) {
      throw new Error('Failed to delete quiz');
    }
  }
};
