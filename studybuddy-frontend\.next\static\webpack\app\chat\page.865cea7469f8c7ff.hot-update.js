"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_ChatHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/ChatHeader */ \"(app-pages-browser)/./src/components/layout/ChatHeader.tsx\");\n/* harmony import */ var _components_layout_ChatInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/ChatInput */ \"(app-pages-browser)/./src/components/layout/ChatInput.tsx\");\n/* harmony import */ var _components_layout_SidebarContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/SidebarContent */ \"(app-pages-browser)/./src/components/layout/SidebarContent.tsx\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* harmony import */ var _components_ui_autoScrollChatArea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/autoScrollChatArea */ \"(app-pages-browser)/./src/components/ui/autoScrollChatArea.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/raise-issue-modal */ \"(app-pages-browser)/./src/components/ui/raise-issue-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// import {subjectOptions} from '@/lib/utils'\n// import SubjectDialog from \"@/components/ui/subjectSectionDialog\";\n\n\n\n\nfunction ChatInterface() {\n    _s();\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedTopic, setSelectedTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [subjectName, setSubjectName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [topicName, setTopicName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showDashboard, setShowDashboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showRaiseIssueModal, setShowRaiseIssueModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Function to replace subject IDs with subject names in responses\n    const replaceSubjectIdsInResponse = (response)=>{\n        if (!subjectName || !selectedSubject) return response;\n        // Replace subject ID with subject name in the response\n        const subjectIdPattern = new RegExp(selectedSubject, 'g');\n        return response.replace(subjectIdPattern, subjectName);\n    };\n    const fetchChatHistory = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat/chat-history\"), {\n                headers: getAuthHeaders()\n            });\n            const responseData = await response.json();\n            const historyData = Array.isArray(responseData.data) ? responseData.data : responseData.data ? [\n                responseData.data\n            ] : [];\n            setChatHistory(historyData);\n            // Process the history data for the current session\n            const sessionData = {};\n            historyData.forEach((item)=>{\n                var _item_subjectWise;\n                (_item_subjectWise = item.subjectWise) === null || _item_subjectWise === void 0 ? void 0 : _item_subjectWise.forEach((subjectData)=>{\n                    if (!sessionData[subjectData.subject]) {\n                        sessionData[subjectData.subject] = [];\n                    }\n                    sessionData[subjectData.subject] = [\n                        ...sessionData[subjectData.subject],\n                        ...subjectData.queries\n                    ];\n                });\n            });\n            setCurrentSession(sessionData);\n        } catch (error) {\n            console.error(\"Error fetching chat history:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            fetchChatHistory();\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Handle subject and topic parameters from URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const subjectFromUrl = searchParams.get('subject');\n            const topicFromUrl = searchParams.get('topic');\n            const subjectNameFromUrl = searchParams.get('subjectName');\n            const topicNameFromUrl = searchParams.get('topicName');\n            if (subjectFromUrl) {\n                setSelectedSubject(subjectFromUrl);\n                setShowDashboard(false);\n            } else {\n                // Only show dashboard if no subject parameter is present\n                setShowDashboard(true);\n            }\n            if (topicFromUrl) {\n                setSelectedTopic(topicFromUrl);\n            }\n            if (subjectNameFromUrl) {\n                setSubjectName(decodeURIComponent(subjectNameFromUrl));\n            }\n            if (topicNameFromUrl) {\n                setTopicName(decodeURIComponent(topicNameFromUrl));\n            }\n        }\n    }[\"ChatInterface.useEffect\"], [\n        searchParams\n    ]);\n    // Redirect to dashboard if no subject is selected and we should show dashboard\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Add a small delay to ensure URL parameters are processed first\n            const timer = setTimeout({\n                \"ChatInterface.useEffect.timer\": ()=>{\n                    if (showDashboard && !selectedSubject && !searchParams.get('subject')) {\n                        router.push('/dashboard');\n                    }\n                }\n            }[\"ChatInterface.useEffect.timer\"], 100);\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearTimeout(timer)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        showDashboard,\n        selectedSubject,\n        router,\n        searchParams\n    ]);\n    // Rest of the component remains the same...\n    const handleSubjectSelect = (subject)=>{\n        setSelectedSubject(subject);\n        setShowDashboard(false);\n        if (currentSession[subject]) {\n            const subjectMessages = currentSession[subject].flatMap((query)=>[\n                    {\n                        content: query.query,\n                        isUser: true,\n                        lastMessage: false\n                    },\n                    {\n                        content: query.response,\n                        isUser: false,\n                        lastMessage: false\n                    }\n                ]);\n            setMessages(subjectMessages);\n        } else {\n            setMessages([]);\n        }\n    };\n    const handleNewSession = ()=>{\n        // Navigate to dashboard instead of showing dialog\n        router.push('/dashboard');\n    };\n    const handleSendMessage = async (message)=>{\n        if (!selectedSubject || !message.trim()) return;\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    content: message,\n                    isUser: true,\n                    lastMessage: false\n                }\n            ]);\n        setIsTyping(true);\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat?subject=\").concat(selectedSubject, \"&query=\").concat(encodeURIComponent(message)), {\n                headers: getAuthHeaders()\n            });\n            const data = await response.json();\n            // Replace subject IDs with subject names in the response\n            const processedResponse = replaceSubjectIdsInResponse(data.response);\n            const newQuery = {\n                query: message,\n                response: processedResponse,\n                tokensUsed: data.tokensUsed || 0,\n                // lastMessage: true,\n                _id: Date.now().toString(),\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        content: processedResponse,\n                        isUser: false,\n                        lastMessage: true\n                    }\n                ]);\n            setIsTyping(false);\n            // Ensure lastMessage is updated only for the latest query\n            setCurrentSession((prev)=>{\n                const updatedQueries = [\n                    ...prev[selectedSubject] || [],\n                    newQuery\n                ];\n                return {\n                    ...prev,\n                    [selectedSubject]: updatedQueries\n                };\n            });\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        content: \"Sorry, there was an error processing your request.\",\n                        isUser: false,\n                        lastMessage: false\n                    }\n                ]);\n        }\n    };\n    const scrollAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function to scroll to bottom\n    const scrollToBottom = ()=>{\n        if (scrollAreaRef.current) {\n            const scrollContainer = scrollAreaRef.current;\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages\n    ]);\n    console.log(messages, 'messages-chat');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen p-2 text-black bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SidebarContent__WEBPACK_IMPORTED_MODULE_4__.SidebarContent, {\n                        onNewSession: handleNewSession,\n                        chatHistory: chatHistory,\n                        onSubjectSelect: handleSubjectSelect,\n                        currentSubject: selectedSubject,\n                        isLoading: isLoading,\n                        currentTopic: selectedTopic,\n                        subjectName: subjectName,\n                        topicName: topicName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col bg-white rounded-[12px] border border-[#309CEC]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_ChatHeader__WEBPACK_IMPORTED_MODULE_2__.ChatHeader, {\n                                subjectName: subjectName,\n                                topicName: topicName,\n                                onRaiseIssue: ()=>setShowRaiseIssueModal(true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_autoScrollChatArea__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                messages: messages,\n                                isTyping: isTyping\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_ChatInput__WEBPACK_IMPORTED_MODULE_3__.ChatInput, {\n                                onSendMessage: handleSendMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_raise_issue_modal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showRaiseIssueModal,\n                onClose: ()=>setShowRaiseIssueModal(false),\n                currentSubject: subjectName,\n                currentTopic: topicName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"czaoKFik17eZVNOHKD58aNkDrtw=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});