"use client"

import { Podium } from "@/components/dashboard/podium"
import { LeaderboardList } from "@/components/dashboard/leaderboard-list"
import { ProfileCard } from "@/components/dashboard/profile-card"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { AppSidebar } from "@/components/dashboard/app-sidebar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { Lightbulb } from "lucide-react"

// Sample data
const leaderboardUsers = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    score: 143,
    avatar: "/placeholder.svg?height=60&width=60",
    studentId: "234015",
    position: 1,
  },
  {
    id: "2",
    name: "<PERSON>",
    score: 120,
    avatar: "/placeholder.svg?height=60&width=60",
    studentId: "234016",
    position: 2,
  },
  {
    id: "3",
    name: "<PERSON><PERSON>h",
    score: 119,
    avatar: "/placeholder.svg?height=60&width=60",
    studentId: "234017",
    position: 3,
  },
]

const currentUser = {
  name: "<PERSON><PERSON><PERSON>",
  score: 143,
  avatar: "/placeholder.svg?height=80&width=80",
  position: 1,
}

export default function LeaderboardPage() {
  return (
    <SidebarProvider>
      <AppSidebar currentPage="dashboard/leaderboard" />
      <SidebarInset>
        <DashboardHeader />

        <main className="flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen">
          {/* Page Title */}
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Leaderboard</h1>
          </div>

          {/* Info Banner */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 flex items-start gap-3">
            <Lightbulb className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-orange-800">
              <strong>Earn sparks (🔥)</strong> and climb the leaderboards by showing up every day, staying consistent,
              and getting your answers right.
            </p>
          </div>

          {/* Podium */}
          <Podium users={leaderboardUsers} />

          {/* Desktop Layout: Two columns */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Leaderboard List */}
            <div className="lg:col-span-2">
              <LeaderboardList users={leaderboardUsers} />
            </div>

            {/* Profile Card - Desktop only */}
            <div className="hidden lg:block">
              <ProfileCard user={currentUser} />
            </div>
          </div>

          {/* Profile Card - Mobile only */}
          <div className="lg:hidden">
            <ProfileCard user={currentUser} />
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
