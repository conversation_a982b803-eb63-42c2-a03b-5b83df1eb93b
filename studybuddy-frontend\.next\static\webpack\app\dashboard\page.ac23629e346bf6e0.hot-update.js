"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/recommendation-card.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationCard: () => (/* binding */ RecommendationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ RecommendationCard auto */ \nvar _s = $RefreshSig$();\n\nfunction RecommendationCard(param) {\n    let { subject, score, color, buttonColor, subjectId, topicId, topicName } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleReviseAgain = ()=>{\n        if (subjectId && topicId) {\n            // Navigate to quiz with specific subject and topic\n            const topicParam = topicName || 'General';\n            router.push(\"/quiz?subject=\".concat(subjectId, \"&topic=\").concat(topicId, \"&subjectName=\").concat(encodeURIComponent(subject), \"&topicName=\").concat(encodeURIComponent(topicParam)));\n        } else if (subjectId) {\n            // Navigate to quiz with just subject\n            router.push(\"/quiz?subject=\".concat(subjectId, \"&subjectName=\").concat(encodeURIComponent(subject)));\n        } else {\n            // Navigate to general quiz page\n            router.push('/quiz');\n        }\n    };\n    // Create display text for the subject/topic\n    const displaySubject = topicName ? \"\".concat(subject, \" - \").concat(topicName) : subject;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(color, \" rounded-lg p-4 space-y-3\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: [\n                            \"You Scored \",\n                            score\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm opacity-90\",\n                        children: [\n                            \"in \",\n                            subject\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"w-full \".concat(buttonColor, \" text-white py-2 px-4 rounded-md text-sm font-medium hover:opacity-90 transition-opacity\"),\n                onClick: handleReviseAgain,\n                children: \"Revise Again\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(RecommendationCard, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = RecommendationCard;\nvar _c;\n$RefreshReg$(_c, \"RecommendationCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\n"));

/***/ })

});