"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leaderboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/podium.tsx":
/*!*********************************************!*\
  !*** ./src/components/dashboard/podium.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Podium: () => (/* binding */ Podium)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Crown,Flame!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Crown,Flame!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Crown,Flame!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n\n\nfunction Podium(param) {\n    let { users, onPeriodChange, onSubjectChange, onClassChange, currentPeriod, currentSubject, currentClass, subjects, classes } = param;\n    const sortedUsers = [\n        ...users\n    ].sort((a, b)=>b.sparkPoints - a.sparkPoints);\n    const [first, second, third] = sortedUsers;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-xl p-6 md:p-8 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-3 mb-12 justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"appearance-none bg-white rounded-full px-6 py-3 pr-10 text-sm font-medium text-gray-700 border-0 focus:ring-2 focus:ring-blue-300 cursor-pointer\",\n                                value: currentPeriod,\n                                onChange: (e)=>onPeriodChange(e.target.value),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"weekly\",\n                                        children: \"Weekly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"monthly\",\n                                        children: \"Monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"all\",\n                                        children: \"All Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"appearance-none bg-white rounded-full px-6 py-3 pr-10 text-sm font-medium text-gray-700 border-0 focus:ring-2 focus:ring-blue-300 cursor-pointer\",\n                                        value: currentSubject,\n                                        onChange: (e)=>onSubjectChange(e.target.value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All Subjects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 15\n                                            }, this),\n                                            subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: subject,\n                                                    children: subject\n                                                }, subject, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"appearance-none bg-white rounded-full px-6 py-3 pr-10 text-sm font-medium text-gray-700 border-0 focus:ring-2 focus:ring-blue-300 cursor-pointer\",\n                                        value: currentClass,\n                                        onChange: (e)=>onClassChange(e.target.value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"All Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, this),\n                                            classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: cls,\n                                                    children: cls\n                                                }, cls, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 pointer-events-none\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end justify-center gap-8 md:gap-16\",\n                children: [\n                    second && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: second.profileImage || \"/assets/buddy/default_profile_pic.png\",\n                                    alt: second.name,\n                                    className: \"w-16 h-16 md:w-20 md:h-20 rounded-full border-4 border-white shadow-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-lg md:text-xl mb-2\",\n                                        children: second.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg px-3 py-2 text-gray-800 font-semibold flex items-center gap-1 shadow-md\",\n                                        children: [\n                                            second.sparkPoints,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-4 w-4 text-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white bg-opacity-40 backdrop-blur-sm rounded-t-xl w-20 h-16 md:w-24 md:h-20 flex items-center justify-center shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-3xl md:text-4xl font-bold\",\n                                    children: \"2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this),\n                    first && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-yellow-400 rounded-full p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: first.avatar || \"/placeholder.svg?height=100&width=100\",\n                                        alt: first.name,\n                                        className: \"w-20 h-20 md:w-24 md:h-24 rounded-full border-4 border-yellow-300 shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-xl md:text-2xl mb-2\",\n                                        children: first.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg px-4 py-2 text-gray-800 font-semibold flex items-center gap-1 shadow-md\",\n                                        children: [\n                                            first.score,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-4 w-4 text-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white bg-opacity-40 backdrop-blur-sm rounded-t-xl w-24 h-24 md:w-28 md:h-28 flex items-center justify-center shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-4xl md:text-5xl font-bold\",\n                                    children: \"1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    third && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: third.avatar || \"/placeholder.svg?height=80&width=80\",\n                                    alt: third.name,\n                                    className: \"w-16 h-16 md:w-20 md:h-20 rounded-full border-4 border-white shadow-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-lg md:text-xl mb-2\",\n                                        children: third.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg px-3 py-2 text-gray-800 font-semibold flex items-center gap-1 shadow-md\",\n                                        children: [\n                                            third.score,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Crown_Flame_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-4 w-4 text-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white bg-opacity-40 backdrop-blur-sm rounded-t-xl w-20 h-12 md:w-24 md:h-16 flex items-center justify-center shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-3xl md:text-4xl font-bold\",\n                                    children: \"3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\podium.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = Podium;\nvar _c;\n$RefreshReg$(_c, \"Podium\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9wb2RpdW0udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF3RDtBQWVqRCxTQUFTRyxPQUFPLEtBVVQ7UUFWUyxFQUNyQkMsS0FBSyxFQUNMQyxjQUFjLEVBQ2RDLGVBQWUsRUFDZkMsYUFBYSxFQUNiQyxhQUFhLEVBQ2JDLGNBQWMsRUFDZEMsWUFBWSxFQUNaQyxRQUFRLEVBQ1JDLE9BQU8sRUFDSyxHQVZTO0lBV3JCLE1BQU1DLGNBQWM7V0FBSVQ7S0FBTSxDQUFDVSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsRUFBRUMsV0FBVyxHQUFHRixFQUFFRSxXQUFXO0lBQzNFLE1BQU0sQ0FBQ0MsT0FBT0MsUUFBUUMsTUFBTSxHQUFHUDtJQUUvQixxQkFDRSw4REFBQ1E7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FDQ0QsV0FBVTtnQ0FDVkUsT0FBT2hCO2dDQUNQaUIsVUFBVSxDQUFDQyxJQUFNckIsZUFBZXFCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs7a0RBRTlDLDhEQUFDSTt3Q0FBT0osT0FBTTtrREFBUzs7Ozs7O2tEQUN2Qiw4REFBQ0k7d0NBQU9KLE9BQU07a0RBQVU7Ozs7OztrREFDeEIsOERBQUNJO3dDQUFPSixPQUFNO2tEQUFNOzs7Ozs7Ozs7Ozs7MENBRXRCLDhEQUFDdEIsbUdBQVdBO2dDQUFDb0IsV0FBVTs7Ozs7Ozs7Ozs7O2tDQUd6Qiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUNDRCxXQUFVO3dDQUNWRSxPQUFPZjt3Q0FDUGdCLFVBQVUsQ0FBQ0MsSUFBTXBCLGdCQUFnQm9CLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs7MERBRS9DLDhEQUFDSTtnREFBT0osT0FBTTswREFBRzs7Ozs7OzRDQUNoQmIsU0FBU2tCLEdBQUcsQ0FBQ0MsQ0FBQUEsd0JBQ1osOERBQUNGO29EQUFxQkosT0FBT007OERBQVVBO21EQUExQkE7Ozs7Ozs7Ozs7O2tEQUdqQiw4REFBQzVCLG1HQUFXQTt3Q0FBQ29CLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FHekIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7d0NBQ0NELFdBQVU7d0NBQ1ZFLE9BQU9kO3dDQUNQZSxVQUFVLENBQUNDLElBQU1uQixjQUFjbUIsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzswREFFN0MsOERBQUNJO2dEQUFPSixPQUFNOzBEQUFHOzs7Ozs7NENBQ2hCWixRQUFRaUIsR0FBRyxDQUFDRSxDQUFBQSxvQkFDWCw4REFBQ0g7b0RBQWlCSixPQUFPTzs4REFBTUE7bURBQWxCQTs7Ozs7Ozs7Ozs7a0RBR2pCLDhEQUFDN0IsbUdBQVdBO3dDQUFDb0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU03Qiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O29CQUVaSCx3QkFDQyw4REFBQ0U7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ1U7b0NBQ0NDLEtBQUtkLE9BQU9lLFlBQVksSUFBSTtvQ0FDNUJDLEtBQUtoQixPQUFPaUIsSUFBSTtvQ0FDaEJkLFdBQVU7Ozs7Ozs7Ozs7OzBDQUdkLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUF5Q0gsT0FBT2lCLElBQUk7Ozs7OztrREFDbkUsOERBQUNmO3dDQUFJQyxXQUFVOzs0Q0FDWkgsT0FBT0YsV0FBVzswREFDbkIsOERBQUNoQixtR0FBS0E7Z0RBQUNxQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3JCLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2U7b0NBQUtmLFdBQVU7OENBQTRDOzs7Ozs7Ozs7Ozs7Ozs7OztvQkFNakVKLHVCQUNDLDhEQUFDRzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUN0QixtR0FBS0E7NENBQUNzQixXQUFVOzs7Ozs7Ozs7OztrREFFbkIsOERBQUNVO3dDQUNDQyxLQUFLZixNQUFNb0IsTUFBTSxJQUFJO3dDQUNyQkgsS0FBS2pCLE1BQU1rQixJQUFJO3dDQUNmZCxXQUFVOzs7Ozs7Ozs7Ozs7MENBR2QsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQTBDSixNQUFNa0IsSUFBSTs7Ozs7O2tEQUNuRSw4REFBQ2Y7d0NBQUlDLFdBQVU7OzRDQUNaSixNQUFNcUIsS0FBSzswREFDWiw4REFBQ3RDLG1HQUFLQTtnREFBQ3FCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHckIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDZTtvQ0FBS2YsV0FBVTs4Q0FBNEM7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU1qRUYsdUJBQ0MsOERBQUNDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNVO29DQUNDQyxLQUFLYixNQUFNa0IsTUFBTSxJQUFJO29DQUNyQkgsS0FBS2YsTUFBTWdCLElBQUk7b0NBQ2ZkLFdBQVU7Ozs7Ozs7Ozs7OzBDQUdkLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUF5Q0YsTUFBTWdCLElBQUk7Ozs7OztrREFDbEUsOERBQUNmO3dDQUFJQyxXQUFVOzs0Q0FDWkYsTUFBTW1CLEtBQUs7MERBQ1osOERBQUN0QyxtR0FBS0E7Z0RBQUNxQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3JCLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2U7b0NBQUtmLFdBQVU7OENBQTRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8xRTtLQTFJZ0JuQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGRhc2hib2FyZFxccG9kaXVtLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDcm93biwgRmxhbWUsIENoZXZyb25Eb3duIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXHJcbmltcG9ydCB7IExlYWRlcmJvYXJkVXNlciB9IGZyb20gXCJAL2xpYi9hcGkvbGVhZGVyYm9hcmRcIlxyXG5cclxuaW50ZXJmYWNlIFBvZGl1bVByb3BzIHtcclxuICB1c2VyczogTGVhZGVyYm9hcmRVc2VyW11cclxuICBvblBlcmlvZENoYW5nZTogKHBlcmlvZDogJ3dlZWtseScgfCAnbW9udGhseScgfCAnYWxsJykgPT4gdm9pZFxyXG4gIG9uU3ViamVjdENoYW5nZTogKHN1YmplY3Q6IHN0cmluZykgPT4gdm9pZFxyXG4gIG9uQ2xhc3NDaGFuZ2U6IChjbGFzc0ZpbHRlcjogc3RyaW5nKSA9PiB2b2lkXHJcbiAgY3VycmVudFBlcmlvZDogc3RyaW5nXHJcbiAgY3VycmVudFN1YmplY3Q6IHN0cmluZ1xyXG4gIGN1cnJlbnRDbGFzczogc3RyaW5nXHJcbiAgc3ViamVjdHM6IHN0cmluZ1tdXHJcbiAgY2xhc3Nlczogc3RyaW5nW11cclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFBvZGl1bSh7XHJcbiAgdXNlcnMsXHJcbiAgb25QZXJpb2RDaGFuZ2UsXHJcbiAgb25TdWJqZWN0Q2hhbmdlLFxyXG4gIG9uQ2xhc3NDaGFuZ2UsXHJcbiAgY3VycmVudFBlcmlvZCxcclxuICBjdXJyZW50U3ViamVjdCxcclxuICBjdXJyZW50Q2xhc3MsXHJcbiAgc3ViamVjdHMsXHJcbiAgY2xhc3Nlc1xyXG59OiBQb2RpdW1Qcm9wcykge1xyXG4gIGNvbnN0IHNvcnRlZFVzZXJzID0gWy4uLnVzZXJzXS5zb3J0KChhLCBiKSA9PiBiLnNwYXJrUG9pbnRzIC0gYS5zcGFya1BvaW50cylcclxuICBjb25zdCBbZmlyc3QsIHNlY29uZCwgdGhpcmRdID0gc29ydGVkVXNlcnNcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTQwMCB2aWEtYmx1ZS01MDAgdG8tYmx1ZS02MDAgcm91bmRlZC14bCBwLTYgbWQ6cC04IHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICB7LyogRmlsdGVyIENvbnRyb2xzICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0zIG1iLTEyIGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFwcGVhcmFuY2Utbm9uZSBiZy13aGl0ZSByb3VuZGVkLWZ1bGwgcHgtNiBweS0zIHByLTEwIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBib3JkZXItMCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTMwMCBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICAgIHZhbHVlPXtjdXJyZW50UGVyaW9kfVxyXG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IG9uUGVyaW9kQ2hhbmdlKGUudGFyZ2V0LnZhbHVlIGFzICd3ZWVrbHknIHwgJ21vbnRobHknIHwgJ2FsbCcpfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwid2Vla2x5XCI+V2Vla2x5PC9vcHRpb24+XHJcbiAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJtb250aGx5XCI+TW9udGhseTwvb3B0aW9uPlxyXG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWxsXCI+QWxsIFRpbWU8L29wdGlvbj5cclxuICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JheS01MDAgcG9pbnRlci1ldmVudHMtbm9uZVwiIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYXBwZWFyYW5jZS1ub25lIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBweC02IHB5LTMgcHItMTAgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJvcmRlci0wIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtMzAwIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17Y3VycmVudFN1YmplY3R9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBvblN1YmplY3RDaGFuZ2UoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPkFsbCBTdWJqZWN0czwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgIHtzdWJqZWN0cy5tYXAoc3ViamVjdCA9PiAoXHJcbiAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17c3ViamVjdH0gdmFsdWU9e3N1YmplY3R9PntzdWJqZWN0fTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JheS01MDAgcG9pbnRlci1ldmVudHMtbm9uZVwiIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhcHBlYXJhbmNlLW5vbmUgYmctd2hpdGUgcm91bmRlZC1mdWxsIHB4LTYgcHktMyBwci0xMCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYm9yZGVyLTAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS0zMDAgY3Vyc29yLXBvaW50ZXJcIlxyXG4gICAgICAgICAgICAgIHZhbHVlPXtjdXJyZW50Q2xhc3N9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBvbkNsYXNzQ2hhbmdlKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5BbGwgQ2xhc3Nlczwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgIHtjbGFzc2VzLm1hcChjbHMgPT4gKFxyXG4gICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2Nsc30gdmFsdWU9e2Nsc30+e2Nsc308L29wdGlvbj5cclxuICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgIDxDaGV2cm9uRG93biBjbGFzc05hbWU9XCJhYnNvbHV0ZSByaWdodC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgaC00IHctNCB0ZXh0LWdyYXktNTAwIHBvaW50ZXItZXZlbnRzLW5vbmVcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIFBvZGl1bSAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBqdXN0aWZ5LWNlbnRlciBnYXAtOCBtZDpnYXAtMTZcIj5cclxuICAgICAgICB7LyogU2Vjb25kIFBsYWNlICovfVxyXG4gICAgICAgIHtzZWNvbmQgJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1iLTZcIj5cclxuICAgICAgICAgICAgICA8aW1nXHJcbiAgICAgICAgICAgICAgICBzcmM9e3NlY29uZC5wcm9maWxlSW1hZ2UgfHwgXCIvYXNzZXRzL2J1ZGR5L2RlZmF1bHRfcHJvZmlsZV9waWMucG5nXCJ9XHJcbiAgICAgICAgICAgICAgICBhbHQ9e3NlY29uZC5uYW1lfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xNiBoLTE2IG1kOnctMjAgbWQ6aC0yMCByb3VuZGVkLWZ1bGwgYm9yZGVyLTQgYm9yZGVyLXdoaXRlIHNoYWRvdy1sZ1wiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtbGcgbWQ6dGV4dC14bCBtYi0yXCI+e3NlY29uZC5uYW1lfTwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC1ncmF5LTgwMCBmb250LXNlbWlib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHNoYWRvdy1tZFwiPlxyXG4gICAgICAgICAgICAgICAge3NlY29uZC5zcGFya1BvaW50c31cclxuICAgICAgICAgICAgICAgIDxGbGFtZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtb3JhbmdlLTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGJnLW9wYWNpdHktNDAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLXQteGwgdy0yMCBoLTE2IG1kOnctMjQgbWQ6aC0yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGdcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZFwiPjI8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIEZpcnN0IFBsYWNlICovfVxyXG4gICAgICAgIHtmaXJzdCAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWItNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0zIGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIGJnLXllbGxvdy00MDAgcm91bmRlZC1mdWxsIHAtMVwiPlxyXG4gICAgICAgICAgICAgICAgPENyb3duIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNjAwXCIgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8aW1nXHJcbiAgICAgICAgICAgICAgICBzcmM9e2ZpcnN0LmF2YXRhciB8fCBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTEwMCZ3aWR0aD0xMDBcIn1cclxuICAgICAgICAgICAgICAgIGFsdD17Zmlyc3QubmFtZX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMjAgaC0yMCBtZDp3LTI0IG1kOmgtMjQgcm91bmRlZC1mdWxsIGJvcmRlci00IGJvcmRlci15ZWxsb3ctMzAwIHNoYWRvdy1sZ1wiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQteGwgbWQ6dGV4dC0yeGwgbWItMlwiPntmaXJzdC5uYW1lfTwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBweC00IHB5LTIgdGV4dC1ncmF5LTgwMCBmb250LXNlbWlib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHNoYWRvdy1tZFwiPlxyXG4gICAgICAgICAgICAgICAge2ZpcnN0LnNjb3JlfVxyXG4gICAgICAgICAgICAgICAgPEZsYW1lIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1vcmFuZ2UtNTAwXCIgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYmctb3BhY2l0eS00MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtdC14bCB3LTI0IGgtMjQgbWQ6dy0yOCBtZDpoLTI4IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC00eGwgbWQ6dGV4dC01eGwgZm9udC1ib2xkXCI+MTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7LyogVGhpcmQgUGxhY2UgKi99XHJcbiAgICAgICAge3RoaXJkICYmIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYi02XCI+XHJcbiAgICAgICAgICAgICAgPGltZ1xyXG4gICAgICAgICAgICAgICAgc3JjPXt0aGlyZC5hdmF0YXIgfHwgXCIvcGxhY2Vob2xkZXIuc3ZnP2hlaWdodD04MCZ3aWR0aD04MFwifVxyXG4gICAgICAgICAgICAgICAgYWx0PXt0aGlyZC5uYW1lfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xNiBoLTE2IG1kOnctMjAgbWQ6aC0yMCByb3VuZGVkLWZ1bGwgYm9yZGVyLTQgYm9yZGVyLXdoaXRlIHNoYWRvdy1sZ1wiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtbGcgbWQ6dGV4dC14bCBtYi0yXCI+e3RoaXJkLm5hbWV9PC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LWdyYXktODAwIGZvbnQtc2VtaWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgc2hhZG93LW1kXCI+XHJcbiAgICAgICAgICAgICAgICB7dGhpcmQuc2NvcmV9XHJcbiAgICAgICAgICAgICAgICA8RmxhbWUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW9yYW5nZS01MDBcIiAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBiZy1vcGFjaXR5LTQwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC10LXhsIHctMjAgaC0xMiBtZDp3LTI0IG1kOmgtMTYgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LWxnXCI+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LTN4bCBtZDp0ZXh0LTR4bCBmb250LWJvbGRcIj4zPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJDcm93biIsIkZsYW1lIiwiQ2hldnJvbkRvd24iLCJQb2RpdW0iLCJ1c2VycyIsIm9uUGVyaW9kQ2hhbmdlIiwib25TdWJqZWN0Q2hhbmdlIiwib25DbGFzc0NoYW5nZSIsImN1cnJlbnRQZXJpb2QiLCJjdXJyZW50U3ViamVjdCIsImN1cnJlbnRDbGFzcyIsInN1YmplY3RzIiwiY2xhc3NlcyIsInNvcnRlZFVzZXJzIiwic29ydCIsImEiLCJiIiwic3BhcmtQb2ludHMiLCJmaXJzdCIsInNlY29uZCIsInRoaXJkIiwiZGl2IiwiY2xhc3NOYW1lIiwic2VsZWN0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvcHRpb24iLCJtYXAiLCJzdWJqZWN0IiwiY2xzIiwiaW1nIiwic3JjIiwicHJvZmlsZUltYWdlIiwiYWx0IiwibmFtZSIsInNwYW4iLCJhdmF0YXIiLCJzY29yZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/podium.tsx\n"));

/***/ })

});