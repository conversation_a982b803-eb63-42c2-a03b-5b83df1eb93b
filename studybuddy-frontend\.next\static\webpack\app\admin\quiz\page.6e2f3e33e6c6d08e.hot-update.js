"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/quiz/page",{

/***/ "(app-pages-browser)/./src/lib/api/quiz.ts":
/*!*****************************!*\
  !*** ./src/lib/api/quiz.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quizApi: () => (/* binding */ quizApi),\n/* harmony export */   subjectApi: () => (/* binding */ subjectApi)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const token = localStorage.getItem('accessToken');\n    return {\n        'Authorization': \"Bearer \".concat(token),\n        'Content-Type': 'application/json'\n    };\n};\n// Subject API functions\nconst subjectApi = {\n    getAll: async ()=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/users/subjects\"), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to fetch subjects');\n        }\n        return response.json();\n    },\n    getById: async (id)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/users/subjects/\").concat(id), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to fetch subject');\n        }\n        return response.json();\n    }\n};\n// Quiz API functions\nconst quizApi = {\n    getAll: async (filter)=>{\n        const params = new URLSearchParams();\n        if (filter === null || filter === void 0 ? void 0 : filter.subjectId) params.append('subjectId', filter.subjectId);\n        if (filter === null || filter === void 0 ? void 0 : filter.topicId) params.append('topicId', filter.topicId);\n        if (filter === null || filter === void 0 ? void 0 : filter.noOfQuestions) params.append('noOfQuestions', filter.noOfQuestions.toString());\n        const queryString = params.toString() ? \"?\".concat(params.toString()) : '';\n        // Try user-facing endpoint first\n        try {\n            const userUrl = \"\".concat(API_BASE_URL, \"/users/quizzes\").concat(queryString);\n            const userResponse = await fetch(userUrl, {\n                headers: getAuthHeaders()\n            });\n            if (userResponse.ok) {\n                return userResponse.json();\n            }\n        } catch (error) {\n            console.log('User quiz endpoint not available, trying admin endpoint');\n        }\n        // Fallback to admin endpoint\n        try {\n            const adminUrl = \"\".concat(API_BASE_URL, \"/admin/quizzes\").concat(queryString);\n            const adminResponse = await fetch(adminUrl, {\n                headers: getAuthHeaders()\n            });\n            if (adminResponse.ok) {\n                return adminResponse.json();\n            }\n        } catch (error) {\n            console.error('Admin quiz endpoint failed:', error);\n        }\n        throw new Error('Failed to fetch quizzes from both user and admin endpoints');\n    },\n    getById: async (id)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes/\").concat(id), {\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to fetch quiz');\n        }\n        return response.json();\n    },\n    create: async (data)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes\"), {\n            method: 'POST',\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to create quiz');\n        }\n        return response.json();\n    },\n    update: async (id, data)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes/\").concat(id), {\n            method: 'PUT',\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error('Failed to update quiz');\n        }\n        return response.json();\n    },\n    delete: async (id)=>{\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/quizzes/\").concat(id), {\n            method: 'DELETE',\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error('Failed to delete quiz');\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/quiz.ts\n"));

/***/ })

});