"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/recommendation-card.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationCard: () => (/* binding */ RecommendationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ RecommendationCard auto */ \nvar _s = $RefreshSig$();\n\nfunction RecommendationCard(param) {\n    let { subject, score, color, buttonColor, subjectId, topicId, topicName } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleReviseAgain = ()=>{\n        if (subjectId && topicId) {\n            // Navigate to quiz with specific subject and topic\n            const topicParam = topicName || 'General';\n            router.push(\"/quiz?subject=\".concat(subjectId, \"&topic=\").concat(topicId, \"&subjectName=\").concat(encodeURIComponent(subject), \"&topicName=\").concat(encodeURIComponent(topicParam)));\n        } else if (subjectId) {\n            // Navigate to quiz with just subject\n            router.push(\"/quiz?subject=\".concat(subjectId, \"&subjectName=\").concat(encodeURIComponent(subject)));\n        } else {\n            // Navigate to general quiz page\n            router.push('/quiz');\n        }\n    };\n    // Create display text for the subject/topic\n    const displaySubject = topicName ? \"\".concat(subject, \" - \").concat(topicName) : subject;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(color, \" rounded-lg p-4 space-y-3\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: score\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm opacity-90\",\n                        children: [\n                            \"in \",\n                            displaySubject\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"w-full \".concat(buttonColor, \" text-white py-2 px-4 rounded-md text-sm font-medium hover:opacity-90 transition-opacity\"),\n                onClick: handleReviseAgain,\n                children: \"Take Quiz\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(RecommendationCard, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = RecommendationCard;\nvar _c;\n$RefreshReg$(_c, \"RecommendationCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\n"));

/***/ })

});