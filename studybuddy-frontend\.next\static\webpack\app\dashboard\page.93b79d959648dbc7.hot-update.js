"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/recommendation-card.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationCard: () => (/* binding */ RecommendationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ RecommendationCard auto */ \nvar _s = $RefreshSig$();\n\nfunction RecommendationCard(param) {\n    let { subject, score, color, buttonColor, subjectId, topicId, topicName } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleReviseAgain = ()=>{\n        if (subjectId && topicId) {\n            // Navigate to quiz with specific subject and topic\n            router.push(\"/quiz?subject=\".concat(subjectId, \"&topic=\").concat(topicId, \"&subjectName=\").concat(encodeURIComponent(subject), \"&topicName=\").concat(encodeURIComponent(subject)));\n        } else {\n            // Navigate to general quiz page\n            router.push('/quiz');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(color, \" rounded-lg p-4 space-y-3\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: [\n                            \"You Scored \",\n                            score\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm opacity-90\",\n                        children: [\n                            \"in \",\n                            subject\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"w-full \".concat(buttonColor, \" text-white py-2 px-4 rounded-md text-sm font-medium hover:opacity-90 transition-opacity\"),\n                onClick: handleReviseAgain,\n                children: \"Revise Again\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(RecommendationCard, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = RecommendationCard;\nvar _c;\n$RefreshReg$(_c, \"RecommendationCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\n"));

/***/ })

});