"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/recommendation-card.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecommendationCard: () => (/* binding */ RecommendationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ RecommendationCard auto */ \nvar _s = $RefreshSig$();\n\nfunction RecommendationCard(param) {\n    let { subject, score, color, buttonColor, subjectId, topicId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const handleReviseAgain = ()=>{\n        if (subjectId && topicId) {\n            // Navigate to quiz with specific subject and topic\n            router.push(\"/quiz?subject=\".concat(subjectId, \"&topic=\").concat(topicId, \"&subjectName=\").concat(encodeURIComponent(subject), \"&topicName=\").concat(encodeURIComponent(subject)));\n        } else {\n            // Navigate to general quiz page\n            router.push('/quiz');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(color, \" rounded-lg p-4 space-y-3\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium\",\n                        children: [\n                            \"You Scored \",\n                            score\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm opacity-90\",\n                        children: [\n                            \"in \",\n                            subject\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"w-full \".concat(buttonColor, \" text-white py-2 px-4 rounded-md text-sm font-medium hover:opacity-90 transition-opacity\"),\n                onClick: handleReviseAgain,\n                children: \"Revise Again\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\dashboard\\\\recommendation-card.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(RecommendationCard, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = RecommendationCard;\nvar _c;\n$RefreshReg$(_c, \"RecommendationCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\n"));

/***/ })

});