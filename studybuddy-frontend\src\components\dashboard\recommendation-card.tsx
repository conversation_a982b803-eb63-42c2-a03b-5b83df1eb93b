"use client"

import { useRouter } from "next/navigation"

interface RecommendationCardProps {
  subject: string
  score: string
  color: string
  buttonColor: string
  subjectId?: string
  topicId?: string
}

export function RecommendationCard({
  subject,
  score,
  color,
  buttonColor,
  subjectId,
  topicId
}: RecommendationCardProps) {
  const router = useRouter()

  const handleReviseAgain = () => {
    if (subjectId && topicId) {
      // Navigate to quiz with specific subject and topic
      router.push(`/quiz?subject=${subjectId}&topic=${topicId}&subjectName=${encodeURIComponent(subject)}&topicName=${encodeURIComponent(subject)}`)
    } else {
      // Navigate to general quiz page
      router.push('/quiz')
    }
  }
  return (
    <div className={`${color} rounded-lg p-4 space-y-3`}>
      <div className="text-sm">
        <div className="font-medium">You Scored {score}</div>
        <div className="text-sm opacity-90">in {subject}</div>
      </div>
      <button
        className={`w-full ${buttonColor} text-white py-2 px-4 rounded-md text-sm font-medium hover:opacity-90 transition-opacity`}
        onClick={handleReviseAgain}
      >
        Revise Again
      </button>
    </div>
  )
}
