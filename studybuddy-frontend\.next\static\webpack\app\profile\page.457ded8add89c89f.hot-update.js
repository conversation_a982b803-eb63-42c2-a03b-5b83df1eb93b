"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/components/layout/activity-heatmap.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/activity-heatmap.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivityHeatmap: () => (/* binding */ ActivityHeatmap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _heatMapGrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heatMapGrid */ \"(app-pages-browser)/./src/components/layout/heatMapGrid.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ActivityHeatmap(param) {\n    let { userData } = param;\n    _s();\n    const [heatMapData, setHeatMapData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [period, setPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"monthly\");\n    function getDateRange() {\n        let period = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'monthly';\n        const endDate = new Date();\n        const startDate = new Date();\n        switch(period){\n            case 'daily':\n                startDate.setDate(startDate.getDate() - 1);\n                break;\n            case 'weekly':\n                startDate.setDate(startDate.getDate() - 7);\n                break;\n            case 'monthly':\n            default:\n                startDate.setMonth(startDate.getMonth() - 1);\n                break;\n        }\n        return [\n            startDate.toISOString().split('T')[0],\n            endDate.toISOString().split('T')[0]\n        ];\n    }\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({\n        \"ActivityHeatmap.useState\": ()=>getDateRange()\n    }[\"ActivityHeatmap.useState\"]);\n    const handlePeriodChange = (value)=>{\n        const range = getDateRange(value);\n        setDateRange(range);\n        setPeriods(value);\n    };\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const fetchHeatMapData = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:3000\", \"/chat/heat-map?upperBound=\").concat(dateRange[1], \"&lowerBound=\").concat(dateRange[0]), {\n                method: 'GET',\n                headers: getAuthHeaders()\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch user data');\n            }\n            const data = await response.json();\n            setHeatMapData(data);\n        } catch (error) {\n            console.error('Error fetching user data:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"ActivityHeatmap.useEffect\": ()=>{\n            fetchHeatMapData();\n        }\n    }[\"ActivityHeatmap.useEffect\"], [\n        fetchHeatMapData\n    ]);\n    const subjectColors = {\n        English: 'bg-red-500',\n        Mathematics: 'bg-blue-500',\n        Geometry: 'bg-green-600',\n        Algebra: 'bg-purple-500',\n        Numerical: 'bg-indigo-400',\n        Science: 'bg-teal-500',\n        Chemistry: 'bg-cyan-400',\n        Biology: 'bg-emerald-500',\n        Physics: 'bg-orange-400',\n        'Social Science': 'bg-pink-500',\n        Geography: 'bg-yellow-500',\n        Economics: 'bg-lime-500',\n        'Political Science': 'bg-rose-500',\n        History: 'bg-amber-500',\n        'Computer Science': 'bg-sky-500',\n        Electronics: 'bg-fuchsia-500',\n        Electricals: 'bg-violet-500',\n        Statistics: 'bg-gray-500',\n        default: 'bg-slate-500'\n    };\n    const getSubjectColor = (subject)=>{\n        return subjectColors[subject] || subjectColors.default;\n    };\n    const getUniqueSubjects = (data)=>{\n        const subjectSet = new Set();\n        data.forEach((entry)=>{\n            entry.subjects.forEach((subject)=>{\n                subjectSet.add(subject);\n            });\n        });\n        return Array.from(subjectSet);\n    };\n    const uniqueSubjects = getUniqueSubjects(heatMapData);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 rounded-[22.5px] border-2 border-blue-200 bg-white h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-[50%] overflow-hidden bg-blue-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/assets/buddy/Joy-profile-icon.svg\",\n                                    alt: \"Recommendation mascot\",\n                                    width: 48,\n                                    height: 48,\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-800\",\n                                        children: \"Here's what you have\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-800\",\n                                        children: [\n                                            \"been sweating on \",\n                                            userData.name,\n                                            \",\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.Select, {\n                            onValueChange: handlePeriodChange,\n                            defaultValue: \"monthly\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectValue, {\n                                        placeholder: \"monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectItem, {\n                                            value: \"monthly\",\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectItem, {\n                                            value: \"weekly\",\n                                            children: \"Weekly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_1__.SelectItem, {\n                                            value: \"daily\",\n                                            children: \"Daily\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 rounded-[14px] border border-[#737373] bg-[#232323]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: \"Subjects Explored\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm font-medium\",\n                                    children: uniqueSubjects === null || uniqueSubjects === void 0 ? void 0 : uniqueSubjects.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap items-center gap-4\",\n                                children: uniqueSubjects === null || uniqueSubjects === void 0 ? void 0 : uniqueSubjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(getSubjectColor(subject))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: subject\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heatMapGrid__WEBPACK_IMPORTED_MODULE_5__.HeatmapGrid, {\n                                heatMapData: heatMapData,\n                                subjectColors: subjectColors,\n                                period: period\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\activity-heatmap.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(ActivityHeatmap, \"Ypi8JXmBvaSTLqe6tfqZiFqd7qA=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ActivityHeatmap;\nvar _c;\n$RefreshReg$(_c, \"ActivityHeatmap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/activity-heatmap.tsx\n"));

/***/ })

});