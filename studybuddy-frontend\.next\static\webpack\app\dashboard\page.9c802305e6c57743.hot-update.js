"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/subject-card */ \"(app-pages-browser)/./src/components/dashboard/subject-card.tsx\");\n/* harmony import */ var _components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/recommendation-card */ \"(app-pages-browser)/./src/components/dashboard/recommendation-card.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(app-pages-browser)/./src/components/dashboard/dashboard-header.tsx\");\n/* harmony import */ var _components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/app-sidebar */ \"(app-pages-browser)/./src/components/dashboard/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/quiz */ \"(app-pages-browser)/./src/lib/api/quiz.ts\");\n/* harmony import */ var _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAuthenticationHook */ \"(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst subjectColors = [\n    \"bg-blue-500\",\n    \"bg-orange-500\",\n    \"bg-red-500\",\n    \"bg-green-500\",\n    \"bg-purple-500\",\n    \"bg-yellow-500\"\n];\nfunction Dashboard() {\n    _s();\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recommendations, setRecommendations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getAuthHeaders } = (0,_hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            loadSubjects();\n            loadRecommendations();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const loadSubjects = async ()=>{\n        try {\n            setLoading(true);\n            const apiSubjects = await _lib_api_quiz__WEBPACK_IMPORTED_MODULE_7__.subjectApi.getAll();\n            const localSubjects = apiSubjects.map((subject, index)=>({\n                    id: subject._id,\n                    subject: subject.name,\n                    color: subjectColors[index % subjectColors.length],\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }));\n            setSubjects(localSubjects);\n        } catch (error) {\n            console.error('Failed to load subjects:', error);\n            // Fallback to default subjects\n            setSubjects([\n                {\n                    id: \"1\",\n                    subject: \"Maths\",\n                    color: \"bg-blue-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"2\",\n                    subject: \"Physics\",\n                    color: \"bg-orange-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"3\",\n                    subject: \"Biology\",\n                    color: \"bg-red-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                },\n                {\n                    id: \"4\",\n                    subject: \"Chemistry\",\n                    color: \"bg-green-500\",\n                    image: \"/placeholder.svg?height=64&width=64\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadRecommendations = async ()=>{\n        // For now, use static recommendations\n        // In a real app, this would fetch user's quiz history and generate recommendations\n        setRecommendations([\n            {\n                subject: \"Algebra\",\n                score: \"6/10\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Algebra\",\n                score: \"6/10\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Velocity\",\n                score: \"6/10\",\n                color: \"bg-orange-50\",\n                buttonColor: \"bg-orange-500\"\n            },\n            {\n                subject: \"Evolution\",\n                score: \"6/10\",\n                color: \"bg-red-50\",\n                buttonColor: \"bg-red-500\"\n            },\n            {\n                subject: \"Evolution\",\n                score: \"6/10\",\n                color: \"bg-red-50\",\n                buttonColor: \"bg-red-500\"\n            },\n            {\n                subject: \"Organic Theory\",\n                score: \"6/10\",\n                color: \"bg-green-50\",\n                buttonColor: \"bg-green-500\"\n            },\n            {\n                subject: \"Algebra\",\n                score: \"6/10\",\n                color: \"bg-blue-50\",\n                buttonColor: \"bg-blue-500\"\n            },\n            {\n                subject: \"Velocity\",\n                score: \"6/10\",\n                color: \"bg-orange-50\",\n                buttonColor: \"bg-orange-500\"\n            }\n        ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_app_sidebar__WEBPACK_IMPORTED_MODULE_5__.AppSidebar, {\n                currentPage: \"dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 md:p-6 space-y-6 bg-gray-50 min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-medium text-gray-800 mb-4\",\n                                    children: \"Welcome back, Student \\uD83D\\uDC4B\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"Continue Learning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: subjects.map((subject, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_subject_card__WEBPACK_IMPORTED_MODULE_2__.SubjectCard, {\n                                                subject: subject.subject,\n                                                color: subject.color,\n                                                image: subject.image,\n                                                subjectId: subject.id\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800\",\n                                                children: \"Smart Recommendations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recommendation_card__WEBPACK_IMPORTED_MODULE_3__.RecommendationCard, {\n                                                subject: rec.subject,\n                                                score: rec.score,\n                                                color: rec.color,\n                                                buttonColor: rec.buttonColor\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"OJt/C7Hd+E1pZMf9rSalgfCehPo=\", false, function() {\n    return [\n        _hooks_useAuthenticationHook__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ3NCO0FBQ2M7QUFDTjtBQUNWO0FBQ1E7QUFDckM7QUFDa0I7QUFDRztBQTBDdkQsTUFBTVcsZ0JBQWdCO0lBQUM7SUFBZTtJQUFpQjtJQUFjO0lBQWdCO0lBQWlCO0NBQWdCO0FBRXZHLFNBQVNDOztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2QsK0NBQVFBLENBQWlCLEVBQUU7SUFDM0QsTUFBTSxDQUFDZSxpQkFBaUJDLG1CQUFtQixHQUFHaEIsK0NBQVFBLENBQXVCLEVBQUU7SUFDL0UsTUFBTSxDQUFDaUIsU0FBU0MsV0FBVyxHQUFHbEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxFQUFFbUIsY0FBYyxFQUFFLEdBQUdULHFFQUFPQTtJQUVsQ1QsZ0RBQVNBOytCQUFDO1lBQ1JtQjtZQUNBQztRQUNGOzhCQUFHLEVBQUU7SUFFTCxNQUFNRCxlQUFlO1FBQ25CLElBQUk7WUFDRkYsV0FBVztZQUNYLE1BQU1JLGNBQWMsTUFBTWIscURBQVVBLENBQUNjLE1BQU07WUFFM0MsTUFBTUMsZ0JBQWdDRixZQUFZRyxHQUFHLENBQUMsQ0FBQ0MsU0FBU0MsUUFBVztvQkFDekVDLElBQUlGLFFBQVFHLEdBQUc7b0JBQ2ZILFNBQVNBLFFBQVFJLElBQUk7b0JBQ3JCQyxPQUFPcEIsYUFBYSxDQUFDZ0IsUUFBUWhCLGNBQWNxQixNQUFNLENBQUM7b0JBQ2xEQyxPQUFPO2dCQUNUO1lBRUFuQixZQUFZVTtRQUNkLEVBQUUsT0FBT1UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQywrQkFBK0I7WUFDL0JwQixZQUFZO2dCQUNWO29CQUFFYyxJQUFJO29CQUFLRixTQUFTO29CQUFTSyxPQUFPO29CQUFlRSxPQUFPO2dCQUFzQztnQkFDaEc7b0JBQUVMLElBQUk7b0JBQUtGLFNBQVM7b0JBQVdLLE9BQU87b0JBQWlCRSxPQUFPO2dCQUFzQztnQkFDcEc7b0JBQUVMLElBQUk7b0JBQUtGLFNBQVM7b0JBQVdLLE9BQU87b0JBQWNFLE9BQU87Z0JBQXNDO2dCQUNqRztvQkFBRUwsSUFBSTtvQkFBS0YsU0FBUztvQkFBYUssT0FBTztvQkFBZ0JFLE9BQU87Z0JBQXNDO2FBQ3RHO1FBQ0gsU0FBVTtZQUNSZixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1HLHNCQUFzQjtRQUMxQixzQ0FBc0M7UUFDdEMsbUZBQW1GO1FBQ25GTCxtQkFBbUI7WUFDakI7Z0JBQUVVLFNBQVM7Z0JBQVdVLE9BQU87Z0JBQVFMLE9BQU87Z0JBQWNNLGFBQWE7WUFBYztZQUNyRjtnQkFBRVgsU0FBUztnQkFBV1UsT0FBTztnQkFBUUwsT0FBTztnQkFBY00sYUFBYTtZQUFjO1lBQ3JGO2dCQUFFWCxTQUFTO2dCQUFZVSxPQUFPO2dCQUFRTCxPQUFPO2dCQUFnQk0sYUFBYTtZQUFnQjtZQUMxRjtnQkFBRVgsU0FBUztnQkFBYVUsT0FBTztnQkFBUUwsT0FBTztnQkFBYU0sYUFBYTtZQUFhO1lBQ3JGO2dCQUFFWCxTQUFTO2dCQUFhVSxPQUFPO2dCQUFRTCxPQUFPO2dCQUFhTSxhQUFhO1lBQWE7WUFDckY7Z0JBQUVYLFNBQVM7Z0JBQWtCVSxPQUFPO2dCQUFRTCxPQUFPO2dCQUFlTSxhQUFhO1lBQWU7WUFDOUY7Z0JBQUVYLFNBQVM7Z0JBQVdVLE9BQU87Z0JBQVFMLE9BQU87Z0JBQWNNLGFBQWE7WUFBYztZQUNyRjtnQkFBRVgsU0FBUztnQkFBWVUsT0FBTztnQkFBUUwsT0FBTztnQkFBZ0JNLGFBQWE7WUFBZ0I7U0FDM0Y7SUFDSDtJQUNBLHFCQUNFLDhEQUFDL0IsbUVBQWVBOzswQkFDZCw4REFBQ0QseUVBQVVBO2dCQUFDaUMsYUFBWTs7Ozs7OzBCQUN4Qiw4REFBQy9CLGdFQUFZQTs7a0NBQ1gsOERBQUNILG1GQUFlQTs7Ozs7a0NBRWhCLDhEQUFDbUM7d0JBQUtDLFdBQVU7OzBDQUVkLDhEQUFDQztnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ0U7b0NBQUdGLFdBQVU7OENBQXlDOzs7Ozs7Ozs7OzswQ0FJekQsOERBQUNHOztrREFDQyw4REFBQ0M7d0NBQUdKLFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3pELDhEQUFDQzt3Q0FBSUQsV0FBVTtrREFDWjNCLFNBQVNZLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQyxzQkFDdEIsOERBQUN6QiwyRUFBV0E7Z0RBRVZ3QixTQUFTQSxRQUFRQSxPQUFPO2dEQUN4QkssT0FBT0wsUUFBUUssS0FBSztnREFDcEJFLE9BQU9QLFFBQVFPLEtBQUs7Z0RBQ3BCWSxXQUFXbkIsUUFBUUUsRUFBRTsrQ0FKaEJEOzs7Ozs7Ozs7Ozs7Ozs7OzBDQVdiLDhEQUFDZ0I7O2tEQUNDLDhEQUFDRjt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNoQywrRUFBR0E7Z0RBQUNnQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNJO2dEQUFHSixXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQ1p6QixnQkFBZ0JVLEdBQUcsQ0FBQyxDQUFDcUIsS0FBS25CLHNCQUN6Qiw4REFBQ3hCLHlGQUFrQkE7Z0RBRWpCdUIsU0FBU29CLElBQUlwQixPQUFPO2dEQUNwQlUsT0FBT1UsSUFBSVYsS0FBSztnREFDaEJMLE9BQU9lLElBQUlmLEtBQUs7Z0RBQ2hCTSxhQUFhUyxJQUFJVCxXQUFXOytDQUp2QlY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFhdkI7R0F0R3dCZjs7UUFJS0YsaUVBQU9BOzs7S0FKWkUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWRhcnNcXERlc2t0b3BcXEZMXFxWZWxvY2l0eVxcc3R1ZHlidWRkeS1mcm9udGVuZFxcc3JjXFxhcHBcXGRhc2hib2FyZFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBTdWJqZWN0Q2FyZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3N1YmplY3QtY2FyZFwiXG5pbXBvcnQgeyBSZWNvbW1lbmRhdGlvbkNhcmQgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9yZWNvbW1lbmRhdGlvbi1jYXJkXCJcbmltcG9ydCB7IERhc2hib2FyZEhlYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2Rhc2hib2FyZC1oZWFkZXJcIlxuaW1wb3J0IHsgQXBwU2lkZWJhciB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2FwcC1zaWRlYmFyXCJcbmltcG9ydCB7IFNpZGViYXJQcm92aWRlciwgU2lkZWJhckluc2V0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zaWRlYmFyXCJcbmltcG9ydCB7IFphcCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgc3ViamVjdEFwaSwgU3ViamVjdCB9IGZyb20gXCJAL2xpYi9hcGkvcXVpelwiXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSBcIkAvaG9va3MvdXNlQXV0aGVudGljYXRpb25Ib29rXCJcblxuaW50ZXJmYWNlIExvY2FsU3ViamVjdCB7XG4gIHN1YmplY3Q6IHN0cmluZ1xuICBjb2xvcjogc3RyaW5nXG4gIGltYWdlOiBzdHJpbmdcbiAgaWQ6IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgUXVpelJlY29tbWVuZGF0aW9uIHtcbiAgc3ViamVjdDogc3RyaW5nXG4gIHNjb3JlOiBzdHJpbmdcbiAgY29sb3I6IHN0cmluZ1xuICBidXR0b25Db2xvcjogc3RyaW5nXG4gIHN1YmplY3RJZD86IHN0cmluZ1xuICB0b3BpY0lkPzogc3RyaW5nXG4gIHRvcGljTmFtZT86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgQ2hhdEhpc3RvcnlJdGVtIHtcbiAgX2lkOiBzdHJpbmdcbiAgZGF0ZTogc3RyaW5nXG4gIHN1YmplY3RXaXNlOiBBcnJheTx7XG4gICAgc3ViamVjdDogc3RyaW5nXG4gICAgcXVlcmllczogQXJyYXk8e1xuICAgICAgcXVlcnk6IHN0cmluZ1xuICAgICAgcmVzcG9uc2U6IHN0cmluZ1xuICAgICAgdG9rZW5zVXNlZDogbnVtYmVyXG4gICAgICBzdW1tYXJ5Pzogc3RyaW5nXG4gICAgICBfaWQ6IHN0cmluZ1xuICAgICAgY3JlYXRlZEF0OiBzdHJpbmdcbiAgICAgIHVwZGF0ZWRBdDogc3RyaW5nXG4gICAgfT5cbiAgICBfaWQ6IHN0cmluZ1xuICB9PlxuICB0b3RhbFRva2Vuc1NwZW50OiBudW1iZXJcbiAgc3ViamVjdHM6IHN0cmluZ1tdXG4gIHVzZXJJZDogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG59XG5cbmNvbnN0IHN1YmplY3RDb2xvcnMgPSBbXCJiZy1ibHVlLTUwMFwiLCBcImJnLW9yYW5nZS01MDBcIiwgXCJiZy1yZWQtNTAwXCIsIFwiYmctZ3JlZW4tNTAwXCIsIFwiYmctcHVycGxlLTUwMFwiLCBcImJnLXllbGxvdy01MDBcIl1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkKCkge1xuICBjb25zdCBbc3ViamVjdHMsIHNldFN1YmplY3RzXSA9IHVzZVN0YXRlPExvY2FsU3ViamVjdFtdPihbXSlcbiAgY29uc3QgW3JlY29tbWVuZGF0aW9ucywgc2V0UmVjb21tZW5kYXRpb25zXSA9IHVzZVN0YXRlPFF1aXpSZWNvbW1lbmRhdGlvbltdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHsgZ2V0QXV0aEhlYWRlcnMgfSA9IHVzZUF1dGgoKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFN1YmplY3RzKClcbiAgICBsb2FkUmVjb21tZW5kYXRpb25zKClcbiAgfSwgW10pXG5cbiAgY29uc3QgbG9hZFN1YmplY3RzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBjb25zdCBhcGlTdWJqZWN0cyA9IGF3YWl0IHN1YmplY3RBcGkuZ2V0QWxsKClcblxuICAgICAgY29uc3QgbG9jYWxTdWJqZWN0czogTG9jYWxTdWJqZWN0W10gPSBhcGlTdWJqZWN0cy5tYXAoKHN1YmplY3QsIGluZGV4KSA9PiAoe1xuICAgICAgICBpZDogc3ViamVjdC5faWQsXG4gICAgICAgIHN1YmplY3Q6IHN1YmplY3QubmFtZSxcbiAgICAgICAgY29sb3I6IHN1YmplY3RDb2xvcnNbaW5kZXggJSBzdWJqZWN0Q29sb3JzLmxlbmd0aF0sXG4gICAgICAgIGltYWdlOiBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTY0JndpZHRoPTY0XCJcbiAgICAgIH0pKVxuXG4gICAgICBzZXRTdWJqZWN0cyhsb2NhbFN1YmplY3RzKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBzdWJqZWN0czonLCBlcnJvcilcbiAgICAgIC8vIEZhbGxiYWNrIHRvIGRlZmF1bHQgc3ViamVjdHNcbiAgICAgIHNldFN1YmplY3RzKFtcbiAgICAgICAgeyBpZDogXCIxXCIsIHN1YmplY3Q6IFwiTWF0aHNcIiwgY29sb3I6IFwiYmctYmx1ZS01MDBcIiwgaW1hZ2U6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9NjQmd2lkdGg9NjRcIiB9LFxuICAgICAgICB7IGlkOiBcIjJcIiwgc3ViamVjdDogXCJQaHlzaWNzXCIsIGNvbG9yOiBcImJnLW9yYW5nZS01MDBcIiwgaW1hZ2U6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9NjQmd2lkdGg9NjRcIiB9LFxuICAgICAgICB7IGlkOiBcIjNcIiwgc3ViamVjdDogXCJCaW9sb2d5XCIsIGNvbG9yOiBcImJnLXJlZC01MDBcIiwgaW1hZ2U6IFwiL3BsYWNlaG9sZGVyLnN2Zz9oZWlnaHQ9NjQmd2lkdGg9NjRcIiB9LFxuICAgICAgICB7IGlkOiBcIjRcIiwgc3ViamVjdDogXCJDaGVtaXN0cnlcIiwgY29sb3I6IFwiYmctZ3JlZW4tNTAwXCIsIGltYWdlOiBcIi9wbGFjZWhvbGRlci5zdmc/aGVpZ2h0PTY0JndpZHRoPTY0XCIgfSxcbiAgICAgIF0pXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbG9hZFJlY29tbWVuZGF0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICAvLyBGb3Igbm93LCB1c2Ugc3RhdGljIHJlY29tbWVuZGF0aW9uc1xuICAgIC8vIEluIGEgcmVhbCBhcHAsIHRoaXMgd291bGQgZmV0Y2ggdXNlcidzIHF1aXogaGlzdG9yeSBhbmQgZ2VuZXJhdGUgcmVjb21tZW5kYXRpb25zXG4gICAgc2V0UmVjb21tZW5kYXRpb25zKFtcbiAgICAgIHsgc3ViamVjdDogXCJBbGdlYnJhXCIsIHNjb3JlOiBcIjYvMTBcIiwgY29sb3I6IFwiYmctYmx1ZS01MFwiLCBidXR0b25Db2xvcjogXCJiZy1ibHVlLTUwMFwiIH0sXG4gICAgICB7IHN1YmplY3Q6IFwiQWxnZWJyYVwiLCBzY29yZTogXCI2LzEwXCIsIGNvbG9yOiBcImJnLWJsdWUtNTBcIiwgYnV0dG9uQ29sb3I6IFwiYmctYmx1ZS01MDBcIiB9LFxuICAgICAgeyBzdWJqZWN0OiBcIlZlbG9jaXR5XCIsIHNjb3JlOiBcIjYvMTBcIiwgY29sb3I6IFwiYmctb3JhbmdlLTUwXCIsIGJ1dHRvbkNvbG9yOiBcImJnLW9yYW5nZS01MDBcIiB9LFxuICAgICAgeyBzdWJqZWN0OiBcIkV2b2x1dGlvblwiLCBzY29yZTogXCI2LzEwXCIsIGNvbG9yOiBcImJnLXJlZC01MFwiLCBidXR0b25Db2xvcjogXCJiZy1yZWQtNTAwXCIgfSxcbiAgICAgIHsgc3ViamVjdDogXCJFdm9sdXRpb25cIiwgc2NvcmU6IFwiNi8xMFwiLCBjb2xvcjogXCJiZy1yZWQtNTBcIiwgYnV0dG9uQ29sb3I6IFwiYmctcmVkLTUwMFwiIH0sXG4gICAgICB7IHN1YmplY3Q6IFwiT3JnYW5pYyBUaGVvcnlcIiwgc2NvcmU6IFwiNi8xMFwiLCBjb2xvcjogXCJiZy1ncmVlbi01MFwiLCBidXR0b25Db2xvcjogXCJiZy1ncmVlbi01MDBcIiB9LFxuICAgICAgeyBzdWJqZWN0OiBcIkFsZ2VicmFcIiwgc2NvcmU6IFwiNi8xMFwiLCBjb2xvcjogXCJiZy1ibHVlLTUwXCIsIGJ1dHRvbkNvbG9yOiBcImJnLWJsdWUtNTAwXCIgfSxcbiAgICAgIHsgc3ViamVjdDogXCJWZWxvY2l0eVwiLCBzY29yZTogXCI2LzEwXCIsIGNvbG9yOiBcImJnLW9yYW5nZS01MFwiLCBidXR0b25Db2xvcjogXCJiZy1vcmFuZ2UtNTAwXCIgfSxcbiAgICBdKVxuICB9XG4gIHJldHVybiAoXG4gICAgPFNpZGViYXJQcm92aWRlcj5cbiAgICAgIDxBcHBTaWRlYmFyIGN1cnJlbnRQYWdlPVwiZGFzaGJvYXJkXCIgLz5cbiAgICAgIDxTaWRlYmFySW5zZXQ+XG4gICAgICAgIDxEYXNoYm9hcmRIZWFkZXIgLz5cblxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgcC00IG1kOnAtNiBzcGFjZS15LTYgYmctZ3JheS01MCBtaW4taC1zY3JlZW5cIj5cbiAgICAgICAgICB7LyogTW9iaWxlIFdlbGNvbWUgTWVzc2FnZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlblwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCBtYi00XCI+V2VsY29tZSBiYWNrLCBTdHVkZW50IPCfkYs8L2gxPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIENvbnRpbnVlIExlYXJuaW5nIFNlY3Rpb24gKi99XG4gICAgICAgICAgPHNlY3Rpb24+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDAgbWItNFwiPkNvbnRpbnVlIExlYXJuaW5nPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgICB7c3ViamVjdHMubWFwKChzdWJqZWN0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxTdWJqZWN0Q2FyZFxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgIHN1YmplY3Q9e3N1YmplY3Quc3ViamVjdH1cbiAgICAgICAgICAgICAgICAgIGNvbG9yPXtzdWJqZWN0LmNvbG9yfVxuICAgICAgICAgICAgICAgICAgaW1hZ2U9e3N1YmplY3QuaW1hZ2V9XG4gICAgICAgICAgICAgICAgICBzdWJqZWN0SWQ9e3N1YmplY3QuaWR9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgICB7LyogU21hcnQgUmVjb21tZW5kYXRpb25zIFNlY3Rpb24gKi99XG4gICAgICAgICAgPHNlY3Rpb24+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTRcIj5cbiAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS01MDBcIiAvPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS04MDBcIj5TbWFydCBSZWNvbW1lbmRhdGlvbnM8L2gyPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICAgICAge3JlY29tbWVuZGF0aW9ucy5tYXAoKHJlYywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8UmVjb21tZW5kYXRpb25DYXJkXG4gICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgc3ViamVjdD17cmVjLnN1YmplY3R9XG4gICAgICAgICAgICAgICAgICBzY29yZT17cmVjLnNjb3JlfVxuICAgICAgICAgICAgICAgICAgY29sb3I9e3JlYy5jb2xvcn1cbiAgICAgICAgICAgICAgICAgIGJ1dHRvbkNvbG9yPXtyZWMuYnV0dG9uQ29sb3J9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L3NlY3Rpb24+XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvU2lkZWJhckluc2V0PlxuICAgIDwvU2lkZWJhclByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTdWJqZWN0Q2FyZCIsIlJlY29tbWVuZGF0aW9uQ2FyZCIsIkRhc2hib2FyZEhlYWRlciIsIkFwcFNpZGViYXIiLCJTaWRlYmFyUHJvdmlkZXIiLCJTaWRlYmFySW5zZXQiLCJaYXAiLCJzdWJqZWN0QXBpIiwidXNlQXV0aCIsInN1YmplY3RDb2xvcnMiLCJEYXNoYm9hcmQiLCJzdWJqZWN0cyIsInNldFN1YmplY3RzIiwicmVjb21tZW5kYXRpb25zIiwic2V0UmVjb21tZW5kYXRpb25zIiwibG9hZGluZyIsInNldExvYWRpbmciLCJnZXRBdXRoSGVhZGVycyIsImxvYWRTdWJqZWN0cyIsImxvYWRSZWNvbW1lbmRhdGlvbnMiLCJhcGlTdWJqZWN0cyIsImdldEFsbCIsImxvY2FsU3ViamVjdHMiLCJtYXAiLCJzdWJqZWN0IiwiaW5kZXgiLCJpZCIsIl9pZCIsIm5hbWUiLCJjb2xvciIsImxlbmd0aCIsImltYWdlIiwiZXJyb3IiLCJjb25zb2xlIiwic2NvcmUiLCJidXR0b25Db2xvciIsImN1cnJlbnRQYWdlIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiIsImgxIiwic2VjdGlvbiIsImgyIiwic3ViamVjdElkIiwicmVjIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuthenticationHook.ts":
/*!********************************************!*\
  !*** ./src/hooks/useAuthenticationHook.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\n\nconst useAuth = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const token = localStorage.getItem('accessToken');\n            if (!token) {\n                router.push('info/start');\n            }\n        }\n    }[\"useAuth.useEffect\"], [\n        router\n    ]);\n    const getAuthHeaders = ()=>({\n            'Authorization': \"Bearer \".concat(localStorage.getItem('accessToken')),\n            'Content-Type': 'application/json'\n        });\n    return {\n        getAuthHeaders\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VBdXRoZW50aWNhdGlvbkhvb2sudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNVO0FBRXBDLE1BQU1FLFVBQVU7SUFDckIsTUFBTUMsU0FBU0YsMERBQVNBO0lBRXhCRCxnREFBU0E7NkJBQUM7WUFDUixNQUFNSSxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMsSUFBSSxDQUFDRixPQUFPO2dCQUNWRCxPQUFPSSxJQUFJLENBQUM7WUFDZDtRQUNGOzRCQUFHO1FBQUNKO0tBQU87SUFFWCxNQUFNSyxpQkFBaUIsSUFBTztZQUM1QixpQkFBaUIsVUFBOEMsT0FBcENILGFBQWFDLE9BQU8sQ0FBQztZQUNoRCxnQkFBZ0I7UUFDbEI7SUFFQSxPQUFPO1FBQUVFO0lBQWU7QUFDMUIsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXFZlbG9jaXR5XFxzdHVkeWJ1ZGR5LWZyb250ZW5kXFxzcmNcXGhvb2tzXFx1c2VBdXRoZW50aWNhdGlvbkhvb2sudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuXHJcbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4ge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhY2Nlc3NUb2tlbicpXHJcbiAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgIHJvdXRlci5wdXNoKCdpbmZvL3N0YXJ0JylcclxuICAgIH1cclxuICB9LCBbcm91dGVyXSlcclxuXHJcbiAgY29uc3QgZ2V0QXV0aEhlYWRlcnMgPSAoKSA9PiAoe1xyXG4gICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FjY2Vzc1Rva2VuJyl9YCxcclxuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICB9KVxyXG5cclxuICByZXR1cm4geyBnZXRBdXRoSGVhZGVycyB9XHJcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlUm91dGVyIiwidXNlQXV0aCIsInJvdXRlciIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInB1c2giLCJnZXRBdXRoSGVhZGVycyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuthenticationHook.ts\n"));

/***/ })

});