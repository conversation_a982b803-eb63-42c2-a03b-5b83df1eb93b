"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/components/layout/heatMapGrid.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/heatMapGrid.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeatmapGrid: () => (/* binding */ HeatmapGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst HeatmapGrid = (param)=>{\n    let { heatMapData, subjectColors, period } = param;\n    let numColumns = 1; // Default for daily\n    // Calculate the number of columns based on the period\n    if (period === \"weekly\") {\n        numColumns = 7; // 7 days in a week\n    } else if (period === \"monthly\") {\n        const currentMonthDays = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate(); // Get the total days in the current month\n        numColumns = currentMonthDays;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-\".concat(numColumns, \" gap-1\"),\n        style: {\n            gridTemplateColumns: \"repeat(\".concat(numColumns, \", minmax(0, 1fr))\")\n        },\n        children: [\n            heatMapData.map((entry, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: entry.subjects.map((subject, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-6 max-sm:w-2 max-sm:h-2 rounded max-sm:rounded-sm border border-blue-200 \".concat(subjectColors[subject])\n                        }, \"cell-\".concat(colIndex, \"-\").concat(rowIndex), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\heatMapGrid.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 7\n                        }, undefined))\n                }, \"col-\".concat(colIndex), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\heatMapGrid.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 3\n                }, undefined)),\n            [\n                ...Array(Math.max(0, numColumns - heatMapData.length))\n            ].map((_, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, rowIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-6 max-sm:w-2 max-sm:h-2 rounded max-sm:rounded-sm bg-black/20 border border-[#A1A1A14D]\"\n                        }, \"empty-\".concat(colIndex, \"-\").concat(rowIndex), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\heatMapGrid.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 9\n                        }, undefined))\n                }, \"empty-col-\".concat(colIndex), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\heatMapGrid.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 5\n                }, undefined))\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\Velocity\\\\studybuddy-frontend\\\\src\\\\components\\\\layout\\\\heatMapGrid.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HeatmapGrid;\nvar _c;\n$RefreshReg$(_c, \"HeatmapGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/heatMapGrid.tsx\n"));

/***/ })

});